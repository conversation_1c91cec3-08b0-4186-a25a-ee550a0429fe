# CLAUDE.md
Do not ever run the app, just ask the user to refresh it. 
This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flutter fitness application with AI-powered voice coaching, real-time workout tracking, and a sophisticated dark theme design system. The app integrates ElevenLabs Conversational AI for voice interactions and Supabase for backend services.

## Essential Commands

```bash
# Development
flutter run -d <device_id>              # Run on specific device
flutter run                             # Run on default device
flutter hot-reload                      # Press 'r' in terminal
flutter hot-restart                     # Press 'R' in terminal

# Build & Code Generation
flutter pub get                         # Install dependencies
flutter packages pub run build_runner build --delete-conflicting-outputs  # Generate freezed models
flutter clean && flutter pub get        # Clean rebuild

# Testing
flutter test                            # Run unit tests
flutter test integration_test/          # Run integration tests
flutter analyze                         # Run static analysis

# Release
flutter build ios --release            # Build iOS release
flutter build apk --release            # Build Android APK
flutter run --release                  # Run in release mode
```

## Architecture & Key Patterns

### Feature-Based Clean Architecture
```
lib/
├── core/               # Shared utilities, theme, constants
├── features/           # Feature modules with layered architecture
│   └── [feature]/
│       ├── presentation/  # UI layer (screens, widgets)
│       ├── domain/       # Business logic (providers, models, services)
│       └── data/         # Data layer (repositories)
└── shared/             # Cross-feature shared code
```

### State Management
- **Riverpod** is used throughout with specific patterns:
  - `StateNotifierProvider` for complex state
  - `FutureProvider` for async data fetching
  - `StreamProvider` for real-time data (auth state)
  - Navigation state managed through `navigationProvider`

### Navigation
- **go_router** with authentication-aware redirects
- Main routes: `/`, `/signin`, `/signup`, `/onboarding`, `/workout-*`
- Redirect logic in `lib/main.dart` checks auth state

## Critical Configuration

### Environment Setup
1. Create `.env` file in project root:
```
OPENAI_API_KEY=your_api_key_here
```

### Supabase Backend
- Local development: `supabase start` (requires Docker)
- Tables: `profiles`, `workouts`, `exercises`, `workout_exercises`, `completed_workouts`
- Real-time subscriptions for auth state changes

## Key Technical Details

### Voice Chat Integration
- WebRTC connection to OpenAI Realtime API
- Audio format: 16kHz, mono, PCM16
- Real-time bidirectional audio streaming
- Function calling for workout commands

### Theme System
- Dark-first design: Background #0A0A0B
- Orange gradient: #FF6B35 → #FF8E53
- Glass morphism cards with backdrop filters
- Spring animations using custom physics
- 4 elevation levels for depth hierarchy

### Code Generation
Models use Freezed for immutability. After modifying any `.freezed.dart` or `.g.dart` files:
```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Performance Considerations
- Widget caching in `PerformanceUtils`
- Lazy loading for workout lists
- Pre-loading next screens during animations
- 60fps target with spring animations

## Common Development Tasks

### Adding a New Feature
1. Create feature folder under `lib/features/`
2. Follow the layered architecture pattern
3. Create provider in `domain/providers/`
4. Add route in `main.dart` router configuration

### Modifying Theme
- Core theme in `lib/core/theme/app_theme.dart`
- Color palette in `lib/core/theme/color_palette.dart`
- Typography in `lib/core/theme/typography.dart`
- Always maintain WCAG AAA contrast ratios

### Working with Voice Chat
- WebRTC implementation: `lib/shared/services/openai_realtime_webrtc_service.dart`
- Hands-free workout screen: `lib/features/workout/presentation/screens/hands_free_workout_screen_webrtc.dart`

### Database Operations
- Use repositories pattern in `data/repositories/`
- Supabase client available via `SupabaseService`
- Always handle offline states and errors

## Platform-Specific Notes

### iOS
- Minimum iOS 11.0
- Info.plist configured for microphone permissions
- Bundle ID: `com.abenezernuro.agenticfit`

### Android
- Minimum API 21
- Package: `com.abenezernuro.agenticfit`
- Microphone permissions in AndroidManifest.xml