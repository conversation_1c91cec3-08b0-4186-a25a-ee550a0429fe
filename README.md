# OpenFit

Your AI-powered fitness companion with voice coaching and real-time workout tracking.

## Getting Started

# OpenFit - Comprehensive Flutter Fitness App

A modern, production-ready fitness application built with Flutter 3.x, featuring advanced UI components, animations, and accessibility support.

## 🎯 Features

### Core Implementation
- **Dark Theme Architecture** with near-black background (#0A0A0B) and 4 elevation levels
- **Orange Gradient System** (#FF6B35 to #FF8E53) for energy/achievement states
- **Complementary Blue Accent** (#4A90E2) for data visualization
- **Success Green** (#00C851) for goal completions
- **WCAG AAA Contrast Ratios** (7:1) for all text

### Typography System
- **Tabular Figures** for numerical displays with consistent alignment
- **Animated Number Counting** effects for metric changes
- **Extra-bold Condensed Fonts** for display numbers
- **Monospace Fonts** for timers and countdowns
- **High Contrast Mode** support

### Advanced UI Components
- **Glass-morphism Cards** with BackdropFilter and gradient borders
- **Floating Navigation** with blur background and morphing icons
- **Animated Charts** with spring physics and interactive data points
- **Circular Progress Rings** with rounded end caps and gradient mesh
- **Skeleton Loading** screens with shimmer effects

### Animation Framework
- **Spring Animations** (SpringSimulation) for all movements
- **Staggered Animations** for multiple elements
- **Counting Animations** for number changes
- **60fps Performance** optimization
- **Reduce Motion** accessibility support

### Gesture System
- **Natural Swipe Gestures** between main sections
- **Physics-based Pull-to-refresh** with haptic feedback
- **Long Press** for contextual menus
- **Pinch-to-zoom** for chart details
- **Haptic Feedback** for all interactions

### Accessibility Features
- **High Contrast Mode** toggle
- **Reduce Motion** settings respect
- **44x44pt Minimum** touch targets
- **Haptic Alternatives** for visual feedback
- **One-handed Operation** support
- **Screen Reader** compatibility

### Performance Optimizations
- **Pre-loading** next screens during idle time
- **Lazy Loading** for complex visualizations
- **Widget Caching** for repeated elements
- **Efficient Rendering** for data visualizations
- **Memory Management** optimization

## 🏗️ Architecture

```
lib/
├── core/
│   ├── animations/           # Animation utilities and components
│   │   ├── animation_utils.dart
│   │   └── counting_animation.dart
│   ├── accessibility/        # Accessibility services
│   │   └── accessibility_service.dart
│   ├── gestures/            # Gesture system
│   │   └── gesture_system.dart
│   ├── performance/         # Performance utilities
│   │   └── performance_utils.dart
│   └── theme/              # Theme system
│       ├── app_theme.dart
│       ├── color_palette.dart
│       ├── typography.dart
│       └── providers/
├── features/
│   ├── dashboard/          # Main dashboard
│   ├── main/              # Navigation structure
│   ├── auth/              # Authentication
│   ├── workout/           # Workout tracking
│   ├── profile/           # User profile
│   └── statistics/        # Data analytics
└── shared/
    ├── widgets/           # Reusable UI components
    │   ├── glass_morphism_card.dart
    │   ├── animated_charts.dart
    │   ├── skeleton_loading.dart
    │   └── themed_components.dart
    └── services/          # Shared services
```

## 🚀 Getting Started

### Prerequisites
- Flutter 3.0 or higher
- Dart 3.0 or higher
- iOS 11.0+ / Android API 21+

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fitness_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 🎨 Design System

### Color Palette
- **Primary Orange**: #FF6B35 (Energy/Achievement)
- **Orange Light**: #FF8E53
- **Accent Blue**: #4A90E2 (Data Visualization)
- **Success Green**: #00C851 (Goal Completions)
- **Dark Background**: #0A0A0B (Near-black with warm tint)
- **Elevation Levels**: #1A1A1B, #2A2A2B, #3A3A3B, #4A4A4B

### Typography
- **Primary Font**: SF Pro Display
- **Secondary Font**: SF Pro Text
- **Monospace Font**: SF Mono
- **Condensed Font**: SF Pro Display Condensed

### Animation Curves
- **Energy Burst**: Elastic out
- **Achievement Pop**: Bounce out
- **Smooth Entry**: Ease out cubic
- **Data Visualization**: Ease in out cubic
- **Card Hover**: Ease out quart

## 📱 Screens

### Dashboard
- Animated metrics cards with counting effects
- Weekly activity chart with spring animations
- Goal progress indicators with gradient rings
- Recent workouts list with glass-morphism cards

### Navigation
- Floating glass navigation bar
- Morphing icon animations
- Haptic feedback on interactions
- Hide-on-scroll behavior

### Accessibility
- High contrast mode support
- Reduced motion preferences
- Screen reader compatibility
- Minimum touch target compliance

## 🔧 Configuration

### Theme Customization
```dart
// Toggle theme mode
ref.read(themeProvider.notifier).setThemeMode(AppThemeMode.dark);

// Enable high contrast
AccessibilityService.instance.toggleHighContrast();

// Reduce motion
AccessibilityService.instance.toggleReduceMotion();
```

### Animation Configuration
```dart
// Custom spring animation
AnimationUtils.animateToWithSpring(
  controller,
  target: 1.0,
  spring: AnimationUtils.bouncySpring,
);

// Staggered animations
final animations = AnimationUtils.createStaggeredAnimations(
  controller: controller,
  count: 4,
  staggerDelay: Duration(milliseconds: 100),
);
```

### Performance Optimization
```dart
// Cache widgets for reuse
PerformanceUtils.cacheWidget('key', widget);

// Execute during idle time
PerformanceUtils.executeOnIdle(() {
  // Expensive operation
});

// Optimized list view
OptimizedListView(
  itemCount: items.length,
  itemBuilder: (context, index) => ItemWidget(items[index]),
  enableLazyLoading: true,
)
```

## 🧪 Testing

### Running Tests
```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Widget tests
flutter test test/widget_test.dart
```

### Performance Testing
```bash
# Profile mode
flutter run --profile

# Performance overlay
flutter run --profile --trace-skia
```

## 📦 Dependencies

### Core Dependencies
- `flutter_riverpod`: State management
- `go_router`: Navigation
- `supabase_flutter`: Backend integration
- `shared_preferences`: Local storage

### UI & Animation
- `animations`: Page transitions
- Custom animation framework

### Development
- `freezed`: Code generation
- `json_annotation`: JSON serialization
- `build_runner`: Code generation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Material Design 3 guidelines
- Accessibility best practices from WCAG
- Performance optimization techniques from Flutter team
