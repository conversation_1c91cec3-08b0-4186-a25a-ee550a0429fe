import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Utility class to handle debounced provider invalidation
/// This prevents rapid successive invalidations that can cause sliver assertion errors
class ProviderUtils {
  static final Map<String, Timer> _debounceTimers = {};
  static const Duration _defaultDebounceDelay = Duration(milliseconds: 300);

  /// Debounced invalidation of a single provider
  static void debouncedInvalidate(
    WidgetRef ref,
    ProviderBase provider, {
    Duration delay = _defaultDebounceDelay,
    String? key,
  }) {
    final timerKey = key ?? provider.toString();
    
    // Cancel existing timer if any
    _debounceTimers[timerKey]?.cancel();
    
    // Create new timer
    _debounceTimers[timerKey] = Timer(delay, () {
      ref.invalidate(provider);
      _debounceTimers.remove(timerKey);
    });
  }

  /// Debounced invalidation of multiple providers
  static void debouncedInvalidateMultiple(
    WidgetRef ref,
    List<ProviderBase> providers, {
    Duration delay = _defaultDebounceDelay,
    String? key,
  }) {
    final timerKey = key ?? 'multiple_${providers.length}';
    
    // Cancel existing timer if any
    _debounceTimers[timerKey]?.cancel();
    
    // Create new timer
    _debounceTimers[timerKey] = Timer(delay, () {
      for (final provider in providers) {
        ref.invalidate(provider);
      }
      _debounceTimers.remove(timerKey);
    });
  }

  /// Refresh dashboard data with debouncing
  static void refreshDashboardData(WidgetRef ref) {
    debouncedInvalidateMultiple(
      ref,
      [
        // Add your dashboard providers here
        // dashboardDataProvider,
        // todayWorkoutProvider,
        // userStatsProvider,
        // allWorkoutsProvider,
        // workoutStatisticsProvider,
        // completedWorkoutsProvider,
      ],
      key: 'dashboard_refresh',
      delay: const Duration(milliseconds: 500),
    );
  }

  /// Cancel all pending debounced operations
  static void cancelAllDebounced() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
  }

  /// Cancel specific debounced operation
  static void cancelDebounced(String key) {
    _debounceTimers[key]?.cancel();
    _debounceTimers.remove(key);
  }
}

/// Extension to make provider invalidation safer
extension SafeProviderInvalidation on WidgetRef {
  /// Safe invalidation that prevents rapid successive calls
  void safeInvalidate(ProviderBase provider, {Duration? delay}) {
    ProviderUtils.debouncedInvalidate(
      this,
      provider,
      delay: delay ?? const Duration(milliseconds: 300),
    );
  }

  /// Safe invalidation of multiple providers
  void safeInvalidateMultiple(List<ProviderBase> providers, {Duration? delay}) {
    ProviderUtils.debouncedInvalidateMultiple(
      this,
      providers,
      delay: delay ?? const Duration(milliseconds: 300),
    );
  }
}

/// Mixin for widgets that need safe provider invalidation
mixin SafeProviderInvalidation {
  void safeInvalidateProvider(
    WidgetRef ref,
    ProviderBase provider, {
    Duration? delay,
  }) {
    ProviderUtils.debouncedInvalidate(
      ref,
      provider,
      delay: delay ?? const Duration(milliseconds: 300),
    );
  }

  void safeInvalidateProviders(
    WidgetRef ref,
    List<ProviderBase> providers, {
    Duration? delay,
  }) {
    ProviderUtils.debouncedInvalidateMultiple(
      ref,
      providers,
      delay: delay ?? const Duration(milliseconds: 300),
    );
  }
}
