import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';

/// Performance optimization utilities for the fitness app
class PerformanceUtils {
  static final Map<String, Widget> _widgetCache = {};
  static final Map<String, dynamic> _dataCache = {};
  
  /// Cache a widget for reuse
  static void cacheWidget(String key, Widget widget) {
    _widgetCache[key] = widget;
  }
  
  /// Get cached widget
  static Widget? getCachedWidget(String key) {
    return _widgetCache[key];
  }
  
  /// Cache data for reuse
  static void cacheData(String key, dynamic data) {
    _dataCache[key] = data;
  }
  
  /// Get cached data
  static T? getCachedData<T>(String key) {
    return _dataCache[key] as T?;
  }
  
  /// Clear all caches
  static void clearCaches() {
    _widgetCache.clear();
    _dataCache.clear();
  }
  
  /// Clear specific cache
  static void clearCache(String key) {
    _widgetCache.remove(key);
    _dataCache.remove(key);
  }
  
  /// Execute callback during idle time
  static void executeOnIdle(VoidCallback callback) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      SchedulerBinding.instance.scheduleFrameCallback((_) {
        callback();
      });
    });
  }
  
  /// Preload widget during idle time
  static void preloadWidget(String key, Widget Function() builder) {
    executeOnIdle(() {
      if (!_widgetCache.containsKey(key)) {
        _widgetCache[key] = builder();
      }
    });
  }
  
  /// Check if device has sufficient performance for animations
  static bool shouldUseAnimations() {
    // Simple heuristic based on frame rate
    return SchedulerBinding.instance.currentFrameTimeStamp.inMicroseconds > 0;
  }
  
  /// Optimize list performance with viewport awareness
  static bool isInViewport(BuildContext context, double itemHeight, int index) {
    final renderObject = context.findRenderObject();
    if (renderObject is! RenderBox) return false;
    
    final viewport = RenderAbstractViewport.of(renderObject);
    
    final itemTop = index * itemHeight;
    final itemBottom = itemTop + itemHeight;
    
    final visibleTop = viewport.getOffsetToReveal(renderObject, 0.0).offset;
    final visibleBottom = visibleTop + viewport.paintBounds.height;
    
    return itemBottom > visibleTop && itemTop < visibleBottom;
  }
}

/// Optimized list view with lazy loading
class OptimizedListView extends StatefulWidget {
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final double itemHeight;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool enableLazyLoading;
  final int preloadBuffer;

  const OptimizedListView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.itemHeight,
    this.controller,
    this.padding,
    this.enableLazyLoading = true,
    this.preloadBuffer = 5,
  });

  @override
  State<OptimizedListView> createState() => _OptimizedListViewState();
}

class _OptimizedListViewState extends State<OptimizedListView> {
  late ScrollController _controller;
  final Set<int> _loadedItems = {};
  final Map<int, Widget> _itemCache = {};

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? ScrollController();
    _controller.addListener(_onScroll);
    
    // Preload initial items
    _preloadItems(0, widget.preloadBuffer);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (!widget.enableLazyLoading) return;
    
    final viewportHeight = _controller.position.viewportDimension;
    final scrollOffset = _controller.offset;
    
    final firstVisibleIndex = (scrollOffset / widget.itemHeight).floor();
    final lastVisibleIndex = ((scrollOffset + viewportHeight) / widget.itemHeight).ceil();
    
    final startIndex = (firstVisibleIndex - widget.preloadBuffer).clamp(0, widget.itemCount - 1);
    final endIndex = (lastVisibleIndex + widget.preloadBuffer).clamp(0, widget.itemCount - 1);
    
    _preloadItems(startIndex, endIndex);
  }

  void _preloadItems(int start, int end) {
    for (int i = start; i <= end; i++) {
      if (!_loadedItems.contains(i)) {
        _loadedItems.add(i);
        PerformanceUtils.executeOnIdle(() {
          if (mounted && !_itemCache.containsKey(i)) {
            _itemCache[i] = widget.itemBuilder(context, i);
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _controller,
      padding: widget.padding,
      itemCount: widget.itemCount,
      itemExtent: widget.itemHeight,
      itemBuilder: (context, index) {
        if (widget.enableLazyLoading && _itemCache.containsKey(index)) {
          return _itemCache[index]!;
        }
        return widget.itemBuilder(context, index);
      },
    );
  }
}

/// Optimized image widget with caching and lazy loading
class OptimizedImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.enableMemoryCache = true,
  });

  @override
  State<OptimizedImage> createState() => _OptimizedImageState();
}

class _OptimizedImageState extends State<OptimizedImage> {
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkVisibility();
    });
  }

  void _checkVisibility() {
    final renderObject = context.findRenderObject();
    if (renderObject is RenderBox) {
      final viewport = RenderAbstractViewport.of(renderObject);
      final position = renderObject.localToGlobal(Offset.zero);
      final size = renderObject.size;
      final viewportBounds = Offset.zero & viewport.paintBounds.size;
      final imageBounds = position & size;
      
      if (viewportBounds.overlaps(imageBounds)) {
        setState(() {
          _isVisible = true;
        });
      }
        }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) {
      return SizedBox(
        width: widget.width,
        height: widget.height,
        child: widget.placeholder ?? const SizedBox.shrink(),
      );
    }

    return Image.network(
      widget.imageUrl,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return widget.placeholder ?? 
          SizedBox(
            width: widget.width,
            height: widget.height,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
      },
      errorBuilder: (context, error, stackTrace) {
        return widget.errorWidget ?? 
          SizedBox(
            width: widget.width,
            height: widget.height,
            child: const Icon(Icons.error),
          );
      },
    );
  }
}

/// Performance monitoring widget
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool enableFPSCounter;
  final bool enableMemoryMonitor;

  const PerformanceMonitor({
    super.key,
    required this.child,
    this.enableFPSCounter = false,
    this.enableMemoryMonitor = false,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor>
    with SingleTickerProviderStateMixin {
  late Ticker _ticker;
  int _frameCount = 0;
  double _fps = 0.0;
  DateTime _lastTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    if (widget.enableFPSCounter) {
      _ticker = createTicker(_onTick);
      _ticker.start();
    }
  }

  @override
  void dispose() {
    if (widget.enableFPSCounter) {
      _ticker.dispose();
    }
    super.dispose();
  }

  void _onTick(Duration elapsed) {
    _frameCount++;
    final now = DateTime.now();
    final timeDiff = now.difference(_lastTime).inMilliseconds;
    
    if (timeDiff >= 1000) {
      setState(() {
        _fps = _frameCount * 1000 / timeDiff;
        _frameCount = 0;
        _lastTime = now;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.enableFPSCounter)
          Positioned(
            top: 50,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'FPS: ${_fps.toStringAsFixed(1)}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// Debounced callback for performance optimization
class DebouncedCallback {
  final Duration delay;
  final VoidCallback callback;
  Timer? _timer;

  DebouncedCallback({
    required this.delay,
    required this.callback,
  });

  void call() {
    _timer?.cancel();
    _timer = Timer(delay, callback);
  }

  void dispose() {
    _timer?.cancel();
  }
}

/// Throttled callback for performance optimization
class ThrottledCallback {
  final Duration interval;
  final VoidCallback callback;
  DateTime? _lastCall;

  ThrottledCallback({
    required this.interval,
    required this.callback,
  });

  void call() {
    final now = DateTime.now();
    if (_lastCall == null || now.difference(_lastCall!) >= interval) {
      _lastCall = now;
      callback();
    }
  }
}
