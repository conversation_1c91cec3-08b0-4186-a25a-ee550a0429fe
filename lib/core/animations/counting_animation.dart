import 'package:flutter/material.dart';
import '../theme/typography.dart';
import 'animation_utils.dart';

/// Animated number counting widget with tabular figures
class CountingAnimation extends StatefulWidget {
  final double value;
  final double? previousValue;
  final TextStyle? textStyle;
  final Duration duration;
  final String? prefix;
  final String? suffix;
  final int decimalPlaces;
  final Curve curve;
  final VoidCallback? onComplete;

  const CountingAnimation({
    super.key,
    required this.value,
    this.previousValue,
    this.textStyle,
    this.duration = const Duration(milliseconds: 800),
    this.prefix,
    this.suffix,
    this.decimalPlaces = 0,
    this.curve = FitnessAnimationCurves.countingCurve,
    this.onComplete,
  });

  @override
  State<CountingAnimation> createState() => _CountingAnimationState();
}

class _CountingAnimationState extends State<CountingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _startValue = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _startValue = widget.previousValue ?? 0.0;
    _setupAnimation();
    _controller.forward().then((_) => widget.onComplete?.call());
  }

  @override
  void didUpdateWidget(CountingAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _startValue = oldWidget.value;
      _controller.reset();
      _setupAnimation();
      _controller.forward().then((_) => widget.onComplete?.call());
    }
  }

  void _setupAnimation() {
    _animation = Tween<double>(
      begin: _startValue,
      end: widget.value,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.curve,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatNumber(double value) {
    if (widget.decimalPlaces == 0) {
      return value.round().toString();
    } else {
      return value.toStringAsFixed(widget.decimalPlaces);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultStyle = AppTypography.tabularFigures(
      fontSize: 24,
      fontWeight: FontWeight.w700,
      color: theme.colorScheme.onSurface,
    );

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final formattedValue = _formatNumber(_animation.value);
        final displayText = '${widget.prefix ?? ''}$formattedValue${widget.suffix ?? ''}';

        return Text(
          displayText,
          style: widget.textStyle ?? defaultStyle,
        );
      },
    );
  }
}

/// Animated progress indicator with counting
class AnimatedProgressCounter extends StatefulWidget {
  final double progress;
  final double? previousProgress;
  final String label;
  final Color? progressColor;
  final Color? backgroundColor;
  final Duration duration;
  final bool showPercentage;
  final TextStyle? labelStyle;
  final TextStyle? valueStyle;

  const AnimatedProgressCounter({
    super.key,
    required this.progress,
    this.previousProgress,
    required this.label,
    this.progressColor,
    this.backgroundColor,
    this.duration = const Duration(milliseconds: 1000),
    this.showPercentage = true,
    this.labelStyle,
    this.valueStyle,
  });

  @override
  State<AnimatedProgressCounter> createState() => _AnimatedProgressCounterState();
}

class _AnimatedProgressCounterState extends State<AnimatedProgressCounter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _setupAnimations();
    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedProgressCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _controller.reset();
      _setupAnimations();
      _controller.forward();
    }
  }

  void _setupAnimations() {
    _progressAnimation = Tween<double>(
      begin: widget.previousProgress ?? 0.0,
      end: widget.progress,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.dataVisualization,
      ),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.achievementPop,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = widget.progressColor ?? theme.colorScheme.primary;
    final backgroundColor = widget.backgroundColor ?? theme.colorScheme.surfaceContainerHighest;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.label,
                    style: widget.labelStyle ?? theme.textTheme.bodyMedium,
                  ),
                  if (widget.showPercentage)
                    CountingAnimation(
                      value: _progressAnimation.value * 100,
                      textStyle: widget.valueStyle ?? AppTypography.tabularFigures(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: progressColor,
                      ),
                      suffix: '%',
                      decimalPlaces: 0,
                    ),
                ],
              ),
              const SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: _progressAnimation.value,
                  backgroundColor: backgroundColor,
                  valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                  minHeight: 6,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Animated metric card with counting and spring animation
class AnimatedMetricCard extends StatefulWidget {
  final String title;
  final double value;
  final double? previousValue;
  final String unit;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;

  const AnimatedMetricCard({
    super.key,
    required this.title,
    required this.value,
    this.previousValue,
    required this.unit,
    required this.icon,
    this.color,
    this.onTap,
  });

  @override
  State<AnimatedMetricCard> createState() => _AnimatedMetricCardState();
}

class _AnimatedMetricCardState extends State<AnimatedMetricCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AnimationUtils.normalDuration,
      vsync: this,
    );

    _scaleAnimation = AnimationUtils.createScaleAnimation(
      controller: _controller,
      curve: FitnessAnimationCurves.springCurve,
    );

    _fadeAnimation = AnimationUtils.createFadeAnimation(
      controller: _controller,
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = widget.color ?? theme.colorScheme.primary;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Card(
              child: InkWell(
                onTap: widget.onTap,
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            widget.icon,
                            color: cardColor,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              widget.title,
                              style: theme.textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.baseline,
                        textBaseline: TextBaseline.alphabetic,
                        children: [
                          CountingAnimation(
                            value: widget.value,
                            previousValue: widget.previousValue,
                            textStyle: AppTypography.displayNumbers(
                              fontSize: 32,
                              color: cardColor,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            widget.unit,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: cardColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
