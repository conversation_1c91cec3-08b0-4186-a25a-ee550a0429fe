import 'package:flutter/material.dart';

/// OpenFit 8pt Grid Spacing System - Exact Implementation
/// Mandatory spacing system for consistent layout across all platforms
class AppSpacing {
  // ==================== 8PT GRID SYSTEM (EXACT SPECIFICATIONS) ====================

  /// Base spacing unit (8px) - Foundation of the grid system
  static const double _baseUnit = 8.0;

  /// Mandatory spacing scale - Use these exact values
  static const double xs = 4.0;   // Micro spacing
  static const double sm = 8.0;   // Small spacing
  static const double md = 16.0;  // Standard spacing
  static const double lg = 24.0;  // Large spacing
  static const double xl = 32.0;  // Extra large spacing
  static const double xxl = 48.0; // Section spacing
  
  // ==================== SEMANTIC SPACING ====================

  /// Semantic aliases for the 8pt grid
  static const double tiny = xs;        // 4px
  static const double small = sm;       // 8px
  static const double medium = md;      // 16px
  static const double large = lg;       // 24px
  static const double extraLarge = xl;  // 32px
  static const double huge = xxl;       // 48px
  
  // Component-specific spacing
  static const double buttonPadding = md;
  static const double cardPadding = md;
  static const double screenPadding = md;
  static const double sectionSpacing = lg;
  static const double itemSpacing = sm;
  
  // ==================== BORDER RADIUS SYSTEM ====================

  /// Border radius scale - Exact specifications from style guide
  static const double radiusXs = 4.0;
  static const double radiusSm = 8.0;
  static const double radiusMd = 12.0;   // Standard button radius
  static const double radiusLg = 16.0;   // Medium card radius
  static const double radiusXl = 20.0;   // Large card radius
  static const double radiusXxl = 24.0;  // Modal radius
  static const double radiusRound = 999.0;
  
  // Icon sizes
  static const double iconXs = 16.0;
  static const double iconSm = 20.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;
  static const double iconXl = 40.0;
  static const double iconXxl = 48.0;
  
  // Avatar sizes
  static const double avatarSm = 32.0;
  static const double avatarMd = 40.0;
  static const double avatarLg = 56.0;
  static const double avatarXl = 80.0;
  static const double avatarXxl = 120.0;
  
  // ==================== BUTTON SPECIFICATIONS ====================

  /// Button heights - Exact specifications from style guide
  static const double buttonHeightSm = 36.0;
  static const double buttonHeightMd = 48.0;  // Minimum 48px touch target
  static const double buttonHeightLg = 56.0;
  
  // Input field heights
  static const double inputHeightSm = 36.0;
  static const double inputHeightMd = 44.0;
  static const double inputHeightLg = 52.0;
  
  // Card dimensions
  static const double cardMinHeight = 80.0;
  static const double cardMaxWidth = 400.0;
  
  // Layout breakpoints
  static const double mobileBreakpoint = 480.0;
  static const double tabletBreakpoint = 768.0;
  static const double desktopBreakpoint = 1024.0;
  
  // Z-index values
  static const double zIndexDropdown = 1000.0;
  static const double zIndexModal = 2000.0;
  static const double zIndexTooltip = 3000.0;
  static const double zIndexToast = 4000.0;
  
  // ==================== ANIMATION SYSTEM (EXACT SPECIFICATIONS) ====================

  /// Animation durations - Exact specifications from style guide
  static const Duration animationFast = Duration(milliseconds: 200);    // --animation-fast
  static const Duration animationNormal = Duration(milliseconds: 300);  // --animation-normal
  static const Duration animationSlow = Duration(milliseconds: 400);    // --animation-slow

  /// Easing curves - Exact specifications from style guide
  static const Curve curveStandard = Cubic(0.4, 0, 0.2, 1);         // --easing-standard
  static const Curve curveBounce = Cubic(0.68, -0.55, 0.265, 1.55); // --easing-bounce
  static const Curve curveDefault = curveStandard;
  static const Curve curveEaseIn = Curves.easeIn;
  static const Curve curveEaseOut = Curves.easeOut;
  
  // Edge insets helpers
  static const EdgeInsets paddingXs = EdgeInsets.all(xs);
  static const EdgeInsets paddingSm = EdgeInsets.all(sm);
  static const EdgeInsets paddingMd = EdgeInsets.all(md);
  static const EdgeInsets paddingLg = EdgeInsets.all(lg);
  static const EdgeInsets paddingXl = EdgeInsets.all(xl);
  static const EdgeInsets paddingXxl = EdgeInsets.all(xxl);
  
  static const EdgeInsets paddingHorizontalXs = EdgeInsets.symmetric(horizontal: xs);
  static const EdgeInsets paddingHorizontalSm = EdgeInsets.symmetric(horizontal: sm);
  static const EdgeInsets paddingHorizontalMd = EdgeInsets.symmetric(horizontal: md);
  static const EdgeInsets paddingHorizontalLg = EdgeInsets.symmetric(horizontal: lg);
  static const EdgeInsets paddingHorizontalXl = EdgeInsets.symmetric(horizontal: xl);
  
  static const EdgeInsets paddingVerticalXs = EdgeInsets.symmetric(vertical: xs);
  static const EdgeInsets paddingVerticalSm = EdgeInsets.symmetric(vertical: sm);
  static const EdgeInsets paddingVerticalMd = EdgeInsets.symmetric(vertical: md);
  static const EdgeInsets paddingVerticalLg = EdgeInsets.symmetric(vertical: lg);
  static const EdgeInsets paddingVerticalXl = EdgeInsets.symmetric(vertical: xl);
  
  // Border radius helpers
  static BorderRadius get borderRadiusXs => BorderRadius.circular(radiusXs);
  static BorderRadius get borderRadiusSm => BorderRadius.circular(radiusSm);
  static BorderRadius get borderRadiusMd => BorderRadius.circular(radiusMd);
  static BorderRadius get borderRadiusLg => BorderRadius.circular(radiusLg);
  static BorderRadius get borderRadiusXl => BorderRadius.circular(radiusXl);
  static BorderRadius get borderRadiusXxl => BorderRadius.circular(radiusXxl);
  static BorderRadius get borderRadiusRound => BorderRadius.circular(radiusRound);
  
  // SizedBox helpers
  static const SizedBox gapXs = SizedBox(height: xs, width: xs);
  static const SizedBox gapSm = SizedBox(height: sm, width: sm);
  static const SizedBox gapMd = SizedBox(height: md, width: md);
  static const SizedBox gapLg = SizedBox(height: lg, width: lg);
  static const SizedBox gapXl = SizedBox(height: xl, width: xl);
  
  static const SizedBox gapVerticalXs = SizedBox(height: xs);
  static const SizedBox gapVerticalSm = SizedBox(height: sm);
  static const SizedBox gapVerticalMd = SizedBox(height: md);
  static const SizedBox gapVerticalLg = SizedBox(height: lg);
  static const SizedBox gapVerticalXl = SizedBox(height: xl);
  
  static const SizedBox gapHorizontalXs = SizedBox(width: xs);
  static const SizedBox gapHorizontalSm = SizedBox(width: sm);
  static const SizedBox gapHorizontalMd = SizedBox(width: md);
  static const SizedBox gapHorizontalLg = SizedBox(width: lg);
  static const SizedBox gapHorizontalXl = SizedBox(width: xl);
}
