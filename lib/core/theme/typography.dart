import 'package:flutter/material.dart';
import 'color_palette.dart';

/// OpenFit Complete Style Guide - Typography System
/// Exact implementation of the mandatory typography specifications
class AppTypography {
  // ==================== FONT STACK (EXACT SPECIFICATIONS) ====================

  /// Required Font Stack - Liberator-Medium with fallbacks
  static const String primaryFontFamily = 'Liberator-Medium';
  static const String fallbackFontFamily = 'SF Pro Display';
  static const String systemFontFamily = '-apple-system';
  static const String monospaceFontFamily = 'SF Mono';

  /// Complete font stack as specified in style guide
  static const List<String> fontFamilyStack = [
    'Liberator-Medium',
    'SF Pro Display',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'sans-serif',
  ];

  /// Additional font families
  static const String condensedFontFamily = 'Liberator-Medium';

  // ==================== FONT WEIGHTS (EXACT SPECIFICATIONS) ====================

  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  static const FontWeight black = FontWeight.w900;

  // ==================== LETTER SPACING (EXACT SPECIFICATIONS) ====================

  static const double displayLetterSpacing = -0.02;  // -0.02em for display
  static const double tightLetterSpacing = -0.01;
  static const double normalLetterSpacing = 0.0;
  static const double wideLetterSpacing = 0.5;

  // ==================== LINE HEIGHT (EXACT SPECIFICATIONS) ====================

  static const double displayLineHeight = 1.1;      // Display typography
  static const double headingLineHeight = 1.2;      // Heading 1
  static const double heading2LineHeight = 1.3;     // Heading 2
  static const double heading3LineHeight = 1.4;     // Heading 3
  static const double bodyLargeLineHeight = 1.6;    // Body large
  static const double bodyRegularLineHeight = 1.5;  // Body regular

  // ==================== MANDATORY TYPE SCALE (EXACT IMPLEMENTATION) ====================

  /// Display Typography
  static TextStyle displayLarge({Color? color}) => TextStyle(
    fontSize: 72,
    fontWeight: bold,
    letterSpacing: displayLetterSpacing,
    height: displayLineHeight,
    fontFamily: primaryFontFamily,
    color: color ?? AppColorPalette.textCreamPrimary,
  );

  /// Heading Hierarchy
  static TextStyle heading1({Color? color}) => TextStyle(
    fontSize: 32,
    fontWeight: semiBold,
    height: headingLineHeight,
    fontFamily: primaryFontFamily,
    color: color ?? AppColorPalette.textCreamPrimary,
  );

  static TextStyle heading2({Color? color}) => TextStyle(
    fontSize: 24,
    fontWeight: semiBold,
    height: heading2LineHeight,
    fontFamily: primaryFontFamily,
    color: color ?? AppColorPalette.textCreamPrimary,
  );

  static TextStyle heading3({Color? color}) => TextStyle(
    fontSize: 20,
    fontWeight: medium,
    height: heading3LineHeight,
    fontFamily: primaryFontFamily,
    color: color ?? AppColorPalette.textCreamPrimary,
  );

  /// Body Text
  static TextStyle bodyLarge({Color? color}) => TextStyle(
    fontSize: 17,
    height: bodyLargeLineHeight,
    fontWeight: regular,
    fontFamily: primaryFontFamily,
    color: color ?? AppColorPalette.textCreamPrimary,
  );

  static TextStyle bodyRegular({Color? color}) => TextStyle(
    fontSize: 15,
    height: bodyRegularLineHeight,
    fontWeight: regular,
    fontFamily: primaryFontFamily,
    color: color ?? AppColorPalette.textCreamPrimary,
  );

  static TextStyle caption({Color? color}) => TextStyle(
    fontSize: 13,
    fontWeight: regular,
    fontFamily: primaryFontFamily,
    color: color ?? AppColorPalette.textCreamSecondary,
  );

  // ==================== LEGACY THEME SUPPORT ====================

  /// Light theme text styles (Updated to cream white system)
  static TextTheme lightTextTheme = TextTheme(
    // Display styles - Updated to cream white system
    displayLarge: TextStyle(
      fontSize: 72,
      fontWeight: bold,
      letterSpacing: displayLetterSpacing,
      height: displayLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: bold,
      letterSpacing: tightLetterSpacing,
      height: displayLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: bold,
      letterSpacing: normalLetterSpacing,
      height: displayLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),

    // Headline styles - Updated to exact specifications
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: headingLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    headlineMedium: TextStyle(
      fontSize: 24,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: heading2LineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    headlineSmall: TextStyle(
      fontSize: 20,
      fontWeight: medium,
      letterSpacing: normalLetterSpacing,
      height: heading3LineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),

    // Title styles - Updated to cream white system
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: headingLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    titleMedium: TextStyle(
      fontSize: 17,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyLargeLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    titleSmall: TextStyle(
      fontSize: 15,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),

    // Body styles - Updated to exact specifications
    bodyLarge: TextStyle(
      fontSize: 17,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyLargeLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    bodyMedium: TextStyle(
      fontSize: 15,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    bodySmall: TextStyle(
      fontSize: 13,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamSecondary,
      fontFamily: primaryFontFamily,
    ),

    // Label styles - Updated to cream white system
    labelLarge: TextStyle(
      fontSize: 16,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    labelMedium: TextStyle(
      fontSize: 15,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    labelSmall: TextStyle(
      fontSize: 13,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamSecondary,
      fontFamily: primaryFontFamily,
    ),
  );
  
  /// Dark theme text styles - PRIMARY THEME (Cream white system)
  static TextTheme darkTextTheme = TextTheme(
    // Display styles - Updated to exact specifications
    displayLarge: TextStyle(
      fontSize: 72,
      fontWeight: bold,
      letterSpacing: displayLetterSpacing,
      height: displayLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: bold,
      letterSpacing: tightLetterSpacing,
      height: displayLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: bold,
      letterSpacing: normalLetterSpacing,
      height: displayLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),

    // Headline styles - Updated to exact specifications
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: headingLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    headlineMedium: TextStyle(
      fontSize: 24,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: heading2LineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    headlineSmall: TextStyle(
      fontSize: 20,
      fontWeight: medium,
      letterSpacing: normalLetterSpacing,
      height: heading3LineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),

    // Title styles - Updated to cream white system
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: headingLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    titleMedium: TextStyle(
      fontSize: 17,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyLargeLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    titleSmall: TextStyle(
      fontSize: 15,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),

    // Body styles - Updated to exact specifications
    bodyLarge: TextStyle(
      fontSize: 17,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyLargeLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    bodyMedium: TextStyle(
      fontSize: 15,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    bodySmall: TextStyle(
      fontSize: 13,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamSecondary,
      fontFamily: primaryFontFamily,
    ),

    // Label styles - Updated to cream white system
    labelLarge: TextStyle(
      fontSize: 16,
      fontWeight: semiBold,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    labelMedium: TextStyle(
      fontSize: 15,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamPrimary,
      fontFamily: primaryFontFamily,
    ),
    labelSmall: TextStyle(
      fontSize: 13,
      fontWeight: regular,
      letterSpacing: normalLetterSpacing,
      height: bodyRegularLineHeight,
      color: AppColorPalette.textCreamSecondary,
      fontFamily: primaryFontFamily,
    ),
  );

  // Specialized text styles for fitness app

  /// Tabular figures for numerical displays with consistent alignment
  static TextStyle tabularFigures({
    required double fontSize,
    required FontWeight fontWeight,
    required Color color,
    bool isDark = false,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: monospaceFontFamily,
      fontFeatures: const [
        FontFeature.tabularFigures(),
        FontFeature.liningFigures(),
      ],
      letterSpacing: 0.5,
      height: 1.0,
    );
  }

  /// Extra-bold condensed fonts for display numbers
  static TextStyle displayNumbers({
    required double fontSize,
    required Color color,
    bool isDark = false,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: black,
      color: color,
      fontFamily: condensedFontFamily,
      fontFeatures: const [
        FontFeature.tabularFigures(),
        FontFeature.liningFigures(),
      ],
      letterSpacing: -1.0,
      height: 0.9,
    );
  }

  /// Monospace fonts for timers and countdowns
  static TextStyle timerText({
    required double fontSize,
    required Color color,
    FontWeight fontWeight = medium,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: monospaceFontFamily,
      fontFeatures: const [
        FontFeature.tabularFigures(),
        FontFeature.liningFigures(),
      ],
      letterSpacing: 1.0,
      height: 1.0,
    );
  }

  /// High contrast text styles for accessibility
  static TextStyle highContrastText({
    required double fontSize,
    required FontWeight fontWeight,
    required Color color,
    double? letterSpacing,
    double? height,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: primaryFontFamily,
      letterSpacing: letterSpacing ?? normalLetterSpacing,
      height: height ?? bodyRegularLineHeight,
      shadows: [
        Shadow(
          offset: const Offset(0, 1),
          blurRadius: 2,
          color: color.withValues(alpha: 0.3),
        ),
      ],
    );
  }

  /// Animated number text style for counting effects
  static TextStyle animatedNumbers({
    required double fontSize,
    required Color color,
    FontWeight fontWeight = extraBold,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: condensedFontFamily,
      fontFeatures: const [
        FontFeature.tabularFigures(),
        FontFeature.liningFigures(),
      ],
      letterSpacing: -0.5,
      height: 1.0,
    );
  }

  /// Button text style for consistent button typography
  static TextStyle buttonTextStyle({Color? color}) => TextStyle(
    fontSize: 16,
    fontWeight: semiBold,
    letterSpacing: normalLetterSpacing,
    height: bodyRegularLineHeight,
    fontFamily: primaryFontFamily,
    color: color ?? AppColorPalette.textCreamPrimary,
  );
}
