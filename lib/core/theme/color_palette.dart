import 'package:flutter/material.dart';

/// OpenFit Complete Style Guide - Exact Color Implementation
/// Navy-and-cream military-inspired color palette with premium aesthetics
class AppColorPalette {
  // ==================== CORE PALETTE (EXACT IMPLEMENTATION) ====================

  /// Primary Colors - Use these exact hex values
  static const Color colorBlackPure = Color(0xFF030303);          // App background base
  static const Color colorNavySurface = Color(0xFF03213b);        // Card backgrounds - User's preferred blue
  static const Color colorNavyElevated = Color(0xFF0A2A45);       // Modal/elevated elements
  static const Color colorNavyBright = Color(0xFF11334F);         // Primary accent/CTAs
  static const Color colorCreamWhite = Color(0xFFFFF8F0);         // Primary text/icons

  /// Secondary Accents - Limited use only
  static const Color colorAccentOrange = Color(0xFFFF6B35);       // High-energy CTAs only
  static const Color colorAccentGold = Color(0xFFFFD60A);         // Achievement badges only
  static const Color colorAccentMint = Color(0xFF00C7BE);         // Success states only

  /// Text Hierarchy - Mandatory opacity levels
  static const Color textCreamPrimary = Color(0xFFFFF8F0);        // Headlines, primary text (opacity 1.0)
  static const Color textCreamSecondary = Color(0xB3FFF8F0);      // Secondary text (opacity 0.7)
  static const Color textCreamTertiary = Color(0x66FFF8F0);       // Placeholder text (opacity 0.4)

  // ==================== LEGACY MAPPINGS (FOR BACKWARD COMPATIBILITY) ====================

  /// Background colors - Updated to exact specifications
  static const Color bgPrimary = colorBlackPure;           // #030303
  static const Color bgSurface = colorNavySurface;         // #112534
  static const Color bgElevated = colorNavyElevated;       // #01202C

  /// Accent colors - Updated to exact specifications
  static const Color accentPrimary = colorNavyBright;      // #0177A9
  static const Color accentSecondary = colorAccentOrange;  // #FF6B35 (energy CTAs only)
  static const Color accentBright = Color(0xFF0090D4);     // Brighter navy for gradients
  static const Color accentGold = colorAccentGold;         // #FFD60A

  /// Text colors - Updated to cream white specifications
  static const Color textPrimary = textCreamPrimary;       // #FFF8F0
  static const Color textSecondary = textCreamSecondary;   // #FFF8F0 70%
  static const Color textTertiary = textCreamTertiary;     // #FFF8F0 40%

  // ==================== SEMANTIC COLORS ====================

  /// Success, warning, error - Updated for navy theme
  static const Color successGreen = colorAccentMint;       // #00C7BE
  static const Color warning = colorAccentGold;            // #FFD60A
  static const Color error = Color(0xFFE74C3C);            // Red alert
  static const Color borderGlass = Color(0x1AFFF8F0);      // Glass border with cream tint (10% opacity)

  // ==================== LEGACY COMPATIBILITY MAPPINGS ====================

  /// Orange system - RESTRICTED to energy CTAs only
  static const Color primaryOrange = accentSecondary;      // #FF6B35 (energy CTAs only)
  static const Color primaryOrangeLight = Color(0xFFFF8E53);
  static const Color primaryOrangeDark = Color(0xFFE55A2B);

  /// Blue system - Updated to navy theme
  static const Color accentBlue = accentPrimary;           // #0177A9
  static const Color accentBlueLight = accentBright;       // #0090D4
  static const Color accentBlueDark = Color(0xFF015A7A);
  static const Color primaryBlue = accentPrimary;          // Legacy compatibility

  /// Success variations - Updated to mint theme
  static const Color successGreenLight = Color(0xFF2DD36F);
  static const Color successGreenDark = Color(0xFF00A844);

  // Dark theme specific colors - Updated to navy theme
  static const Color darkBackground = bgPrimary;
  static const Color elevation1 = bgSurface;
  static const Color elevation2 = bgElevated;
  static const Color elevation3 = Color(0xFF1A3A47);
  static const Color elevation4 = Color(0xFF2A4A57);

  // ==================== NEUTRAL COLORS (UPDATED FOR CREAM THEME) ====================

  /// Primary neutrals - Updated to cream white system
  static const Color white = colorCreamWhite;              // #FFF8F0 (NO pure white allowed)
  static const Color black = colorBlackPure;               // #030303

  /// Grey scale - Navy-tinted for consistency
  static const Color grey50 = Color(0xFFFAF9F7);          // Cream-tinted light
  static const Color grey100 = Color(0xFFF5F4F2);         // Cream-tinted
  static const Color grey200 = Color(0xFFE5E4E2);         // Cream-tinted
  static const Color grey300 = Color(0xFFD4D3D1);         // Cream-tinted
  static const Color grey400 = Color(0xFFA3A2A0);         // Cream-tinted
  static const Color grey500 = Color(0xFF737270);         // Cream-tinted
  static const Color grey600 = Color(0xFF525150);         // Cream-tinted
  static const Color grey700 = Color(0xFF403F3E);         // Cream-tinted
  static const Color grey800 = Color(0xFF262524);         // Cream-tinted
  static const Color grey900 = Color(0xFF171615);         // Cream-tinted dark

  // Semantic colors - Updated to use navy theme colors
  static const Color success = successGreen;
  static const Color warningYellow = warning; // Use navy theme warning
  static const Color errorRed = error; // Use navy theme error
  static const Color info = accentBlue;
  
  // Card colors for dark theme
  static const Color darkCard = elevation2;
  
  // Additional accent colors
  static const Color accentPurple = Color(0xFF8B5CF6);
  static const Color accentPurpleLight = Color(0xFFA78BFA);
  static const Color accentPurpleDark = Color(0xFF7C3AED);
  
  // Fitness-specific colors
  static const Color cardio = Color(0xFFFF6B6B);
  static const Color strength = Color(0xFF4ECDC4);
  static const Color flexibility = Color(0xFFFFE66D);
  static const Color endurance = Color(0xFF95E1D3);
  
  // ==================== THEME COLOR SCHEMES ====================

  /// Light theme colors - Navy theme (DARK-ONLY APP - Light theme for system compatibility)
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: colorNavyBright,                    // #0177A9
    onPrimary: colorCreamWhite,                  // #FFF8F0
    primaryContainer: colorNavyElevated,         // #01202C
    onPrimaryContainer: colorCreamWhite,         // #FFF8F0
    secondary: colorAccentOrange,                // #FF6B35 (energy CTAs only)
    onSecondary: colorCreamWhite,                // #FFF8F0
    secondaryContainer: colorNavySurface,        // #112534
    onSecondaryContainer: textCreamSecondary,    // #FFF8F0 70%
    tertiary: colorAccentMint,                   // #00C7BE
    onTertiary: colorCreamWhite,                 // #FFF8F0
    tertiaryContainer: colorNavySurface,         // #112534
    onTertiaryContainer: textCreamSecondary,     // #FFF8F0 70%
    error: error,                                // #E74C3C
    onError: colorCreamWhite,                    // #FFF8F0
    errorContainer: colorNavySurface,            // #112534
    onErrorContainer: textCreamSecondary,        // #FFF8F0 70%
    surface: colorNavySurface,                   // #112534
    onSurface: colorCreamWhite,                  // #FFF8F0
    surfaceContainerHighest: colorNavyElevated,  // #01202C
    onSurfaceVariant: textCreamSecondary,        // #FFF8F0 70%
    outline: borderGlass,                        // Glass border
    outlineVariant: borderGlass,                 // Glass border
    shadow: colorBlackPure,                      // #030303
    scrim: colorBlackPure,                       // #030303
    inverseSurface: colorCreamWhite,             // #FFF8F0
    onInverseSurface: colorBlackPure,            // #030303
    inversePrimary: accentBright,                // #0090D4
  );
  
  /// Dark theme colors - PRIMARY THEME (Navy with near-black background)
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: accentBright,                       // #0090D4 (Bright navy)
    onPrimary: colorCreamWhite,                  // #FFF8F0
    primaryContainer: colorNavyBright,           // #0177A9
    onPrimaryContainer: colorCreamWhite,         // #FFF8F0
    secondary: colorAccentOrange,                // #FF6B35 (energy CTAs only)
    onSecondary: colorCreamWhite,                // #FFF8F0
    secondaryContainer: colorNavyElevated,       // #01202C
    onSecondaryContainer: textCreamSecondary,    // #FFF8F0 70%
    tertiary: colorAccentMint,                   // #00C7BE
    onTertiary: colorCreamWhite,                 // #FFF8F0
    tertiaryContainer: colorNavyElevated,        // #01202C
    onTertiaryContainer: textCreamSecondary,     // #FFF8F0 70%
    error: Color(0xFFFCA5A5),                    // Light red for dark theme
    onError: colorBlackPure,                     // #030303
    errorContainer: Color(0xFFDC2626),           // Red container
    onErrorContainer: colorCreamWhite,           // #FFF8F0
    surface: colorNavySurface,                   // #112534
    onSurface: colorCreamWhite,                  // #FFF8F0
    surfaceContainerHighest: colorNavyElevated,  // #01202C
    onSurfaceVariant: textCreamSecondary,        // #FFF8F0 70%
    outline: borderGlass,                        // Glass border
    outlineVariant: borderGlass,                 // Glass border
    shadow: colorBlackPure,                      // #030303
    scrim: colorBlackPure,                       // #030303
    inverseSurface: colorCreamWhite,             // #FFF8F0
    onInverseSurface: colorBlackPure,            // #030303
    inversePrimary: colorNavyBright,             // #0177A9
  );
  
  // ==================== GRADIENT DEFINITIONS ====================

  /// Primary navy gradient - Default for most CTAs
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [colorNavyBright, accentBright],     // #0177A9 → #0090D4
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Energy gradient - ONLY for workout start buttons
  static const LinearGradient energyGradient = LinearGradient(
    colors: [colorAccentOrange, Color(0xFFFF8E53)],  // #FF6B35 → #FF8E53
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Achievement gradient - Gold for badges only
  static const LinearGradient achievementGradient = LinearGradient(
    colors: [colorAccentGold, Color(0xFFFFE55C)],    // #FFD60A → lighter gold
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Success gradient - Mint for success states
  static const LinearGradient successGradient = LinearGradient(
    colors: [colorAccentMint, Color(0xFF2DD4CD)],    // #00C7BE → lighter mint
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Data visualization gradient - Navy theme
  static const LinearGradient dataVisualizationGradient = LinearGradient(
    colors: [colorNavyBright, accentBright],     // #0177A9 → #0090D4
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ==================== LEGACY GRADIENT MAPPINGS ====================

  /// Legacy mappings for backward compatibility
  static const LinearGradient primaryEnergyGradient = energyGradient;
  static const LinearGradient accentGradient = energyGradient;
  static const LinearGradient secondaryGradient = primaryGradient;

  // ==================== GLASS MORPHISM SYSTEM ====================

  /// Glass morphism flat color - Enhanced visibility for navy theme
  static const Color glassMorphismColor = Color(0x80032139);  // User's navy with 50% opacity

  /// Dark glass morphism flat color - Enhanced for better visibility
  static const Color darkGlassMorphismColor = Color(0x66032139);  // User's navy with 40% opacity

  /// Glass border color for cards - Enhanced visibility
  static const Color glassBorder = Color(0x33FFF8F0);      // Cream white 20% opacity (more visible)

  // ==================== SURFACE COLORS (UPDATED FOR NAVY THEME) ====================

  /// Surface colors for cards and containers - Navy theme only
  static Color lightSurface = colorNavySurface;            // #112534
  static Color lightSurfaceVariant = colorNavyElevated;    // #01202C
  static Color darkSurface = colorNavySurface;             // #112534
  static Color darkSurfaceVariant = colorNavyElevated;     // #01202C
  static Color darkSurfaceElevated = elevation3;           // Elevated navy
  static Color darkSurfaceHighest = elevation4;            // Highest navy

  // ==================== TEXT COLORS (CREAM WHITE SYSTEM) ====================

  /// Text colors with WCAG AAA contrast ratios - Cream white system
  static Color lightTextPrimary = colorCreamWhite;         // #FFF8F0
  static Color lightTextSecondary = textCreamSecondary;    // #FFF8F0 70%
  static Color lightTextTertiary = textCreamTertiary;      // #FFF8F0 40%
  static Color darkTextPrimary = colorCreamWhite;          // #FFF8F0
  static Color darkTextSecondary = textCreamSecondary;     // #FFF8F0 70%
  static Color darkTextTertiary = textCreamTertiary;       // #FFF8F0 40%

  // ==================== HIGH CONTRAST VARIANTS ====================

  /// High contrast variants for accessibility
  static Color highContrastLight = colorBlackPure;         // #030303
  static Color highContrastDark = colorCreamWhite;         // #FFF8F0
  static Color highContrastBackground = colorCreamWhite;   // #FFF8F0
  static Color highContrastSurface = colorBlackPure;      // #030303
}
