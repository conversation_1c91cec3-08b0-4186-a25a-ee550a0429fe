import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:glossy/glossy.dart';
import 'color_palette.dart';

/// Glass Morphism System for OpenFit
/// Implements the exact glass morphism specifications from the style guide
class GlassMorphism {
  GlassMorphism._();

  // ==================== GLASS MORPHISM DECORATIONS ====================

  /// Standard glass card decoration - Flat design with minimal rounding
  static BoxDecoration get glassCard => BoxDecoration(
    color: AppColorPalette.colorNavySurface.withValues(alpha: 0.5),  // Flat navy color
    borderRadius: BorderRadius.circular(8),  // Less rounded
    border: Border.all(
      color: AppColorPalette.glassBorder,  // Enhanced cream white border
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorBlackPure.withValues(alpha: 0.3),  // Stronger shadow
        blurRadius: 32,
        offset: const Offset(0, 8),
      ),
    ],
  );

  /// Large glass card decoration
  static BoxDecoration get glassCardLarge => glassCard.copyWith(
    borderRadius: BorderRadius.circular(12),  // Less rounded
  );

  /// Medium glass card decoration
  static BoxDecoration get glassCardMedium => glassCard.copyWith(
    borderRadius: BorderRadius.circular(8),  // Less rounded
  );

  /// Small glass card decoration
  static BoxDecoration get glassCardSmall => glassCard.copyWith(
    borderRadius: BorderRadius.circular(6),  // Less rounded
  );

  // ==================== GLASS MORPHISM CONTAINERS ====================

  /// Flat glossy container with minimal border radius - Tesla-inspired
  static Widget glassContainer({
    required Widget child,
    double borderRadius = 6, // Even less rounded
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double opacity = 0.15, // Reduced for subtle effect
    double strengthX = 12, // Reduced blur for crisp look
    double strengthY = 12,
  }) {
    return Container(
      margin: margin,
      child: GlossyContainer(
        width: width ?? double.infinity,
        height: height ?? 200,
        borderRadius: BorderRadius.circular(borderRadius),
        strengthX: strengthX,
        strengthY: strengthY,
        opacity: opacity,
        color: AppColorPalette.colorNavySurface,
        border: Border.all(
          color: AppColorPalette.glassBorder,
          width: 0.5, // Thinner border for flat look
        ),
        child: Container(
          padding: padding ?? const EdgeInsets.all(16), // Reduced padding
          child: child,
        ),
      ),
    );
  }

  /// Flat premium glass card - Tesla dashboard style
  static Widget premiumGlassCard({
    required Widget child,
    double borderRadius = 8, // Less rounded
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double opacity = 0.18, // Subtle transparency
    double strengthX = 15, // Crisp blur
    double strengthY = 15,
  }) {
    return Container(
      margin: margin,
      child: GlossyContainer(
        width: width ?? double.infinity,
        height: height ?? 250,
        borderRadius: BorderRadius.circular(borderRadius),
        strengthX: strengthX,
        strengthY: strengthY,
        opacity: opacity,
        color: AppColorPalette.colorNavyElevated,
        border: Border.all(
          color: AppColorPalette.glassBorder,
          width: 0.5, // Minimal border
        ),
        boxShadow: [
          BoxShadow(
            color: AppColorPalette.colorNavyBright.withValues(alpha: 0.05), // Subtle shadow
            blurRadius: 20, // Reduced shadow
            offset: const Offset(0, 4), // Closer to surface
          ),
        ],
        child: Container(
          padding: padding ?? const EdgeInsets.all(20), // Balanced padding
          child: child,
        ),
      ),
    );
  }

  // ==================== GLOSSY ENHANCED COMPONENTS ====================

  /// Flat glossy FAB - Tesla style with minimal rounding
  static Widget glossyFAB({
    required VoidCallback onPressed,
    required Widget child,
    double size = 56,
    Color? color,
  }) {
    return GlossyContainer(
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(8), // Even less rounded
      strengthX: 10, // Reduced blur for crisp look
      strengthY: 10,
      opacity: 0.2, // Subtle transparency
      color: color ?? AppColorPalette.colorNavyBright,
      border: Border.all(
        color: AppColorPalette.glassBorder,
        width: 0.5, // Minimal border
      ),
      boxShadow: [
        BoxShadow(
          color: AppColorPalette.colorNavyBright.withValues(alpha: 0.15), // Subtle shadow
          blurRadius: 12, // Reduced shadow
          offset: const Offset(0, 4), // Closer to surface
        ),
      ],
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onPressed,
          child: Center(child: child),
        ),
      ),
    );
  }

  /// Flat navigation bar - Tesla dashboard style
  static Widget glossyNavBar({
    required Widget child,
    double height = 80,
    EdgeInsetsGeometry? padding,
  }) {
    return GlossyContainer(
      width: double.infinity,
      height: height,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(6), // Even less rounded
        topRight: Radius.circular(6),
      ),
      strengthX: 12, // Crisp blur
      strengthY: 12,
      opacity: 0.18, // Subtle transparency
      color: AppColorPalette.colorNavySurface,
      border: Border.all(
        color: AppColorPalette.glassBorder,
        width: 0.5, // Minimal border
      ),
      child: Container(
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        child: child,
      ),
    );
  }

  /// Flat modal/dialog - Tesla interface style
  static Widget glossyModal({
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.all(20),
      child: GlossyContainer(
        width: width ?? double.infinity,
        height: height ?? 400,
        borderRadius: BorderRadius.circular(10), // Even less rounded
        strengthX: 18, // Crisp blur
        strengthY: 18,
        opacity: 0.15, // Subtle transparency
        color: AppColorPalette.colorNavyElevated,
        border: Border.all(
          color: AppColorPalette.glassBorder,
          width: 0.5, // Minimal border
        ),
        boxShadow: [
          BoxShadow(
            color: AppColorPalette.colorBlackPure.withValues(alpha: 0.2), // Subtle shadow
            blurRadius: 24, // Reduced shadow
            offset: const Offset(0, 8), // Closer to surface
          ),
        ],
        child: Container(
          padding: padding ?? const EdgeInsets.all(24), // Balanced padding
          child: child,
        ),
      ),
    );
  }

  // ==================== GLASS MORPHISM VARIANTS ====================

  /// Flat elevated glass decoration - Tesla style
  static BoxDecoration get glassElevated => BoxDecoration(
    color: AppColorPalette.colorNavyElevated.withValues(alpha: 0.4), // Reduced opacity
    borderRadius: BorderRadius.circular(8), // Less rounded
    border: Border.all(
      color: AppColorPalette.glassBorder,
      width: 0.5, // Minimal border
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorBlackPure.withValues(alpha: 0.15), // Subtle shadow
        blurRadius: 20, // Reduced blur
        offset: const Offset(0, 6), // Closer to surface
      ),
    ],
  );

  /// Flat floating glass decoration - Tesla style
  static BoxDecoration get glassFloating => BoxDecoration(
    color: AppColorPalette.colorNavySurface.withValues(alpha: 0.5), // Reduced opacity
    borderRadius: BorderRadius.circular(6), // Less rounded
    border: Border.all(
      color: AppColorPalette.glassBorder,
      width: 0.5, // Minimal border
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorNavyBright.withValues(alpha: 0.15), // Subtle shadow
        blurRadius: 12, // Reduced blur
        offset: const Offset(0, 4), // Closer to surface
      ),
    ],
  );

  // ==================== GLASS MORPHISM UTILITIES ====================

  /// Get glass color with specified opacity
  static Color glassColor([double opacity = 0.4]) {
    return AppColorPalette.colorNavySurface.withValues(alpha: opacity);
  }

  /// Get glass border color with specified opacity
  static Color glassBorderColor([double opacity = 0.1]) {
    return AppColorPalette.colorCreamWhite.withValues(alpha: opacity);
  }

  /// Create custom flat glass decoration - Tesla style defaults
  static BoxDecoration customGlass({
    double opacity = 0.25, // Reduced default opacity
    double borderRadius = 6, // Even less rounded default
    double borderOpacity = 0.05, // Minimal border
    double shadowOpacity = 0.1, // Subtle shadow
    double shadowBlur = 16, // Reduced blur
    Offset shadowOffset = const Offset(0, 4), // Closer to surface
  }) {
    return BoxDecoration(
      color: AppColorPalette.colorNavySurface.withValues(alpha: opacity),
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: AppColorPalette.colorCreamWhite.withValues(alpha: borderOpacity),
        width: 0.5, // Minimal border width
      ),
      boxShadow: [
        BoxShadow(
          color: AppColorPalette.colorBlackPure.withValues(alpha: shadowOpacity),
          blurRadius: shadowBlur,
          offset: shadowOffset,
        ),
      ],
    );
  }
}
