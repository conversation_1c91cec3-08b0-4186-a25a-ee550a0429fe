import 'dart:ui';
import 'package:flutter/material.dart';
import 'color_palette.dart';

/// Glass Morphism System for OpenFit
/// Implements the exact glass morphism specifications from the style guide
class GlassMorphism {
  GlassMorphism._();

  // ==================== GLASS MORPHISM DECORATIONS ====================

  /// Standard glass card decoration - Enhanced visibility
  static BoxDecoration get glassCard => BoxDecoration(
    color: AppColorPalette.colorNavySurface.withValues(alpha: 0.5),  // rgba(17, 37, 52, 0.5) - more visible
    borderRadius: BorderRadius.circular(20),
    border: Border.all(
      color: AppColorPalette.glassBorder,  // Enhanced cream white border
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorBlackPure.withValues(alpha: 0.3),  // Stronger shadow
        blurRadius: 32,
        offset: const Offset(0, 8),
      ),
    ],
  );

  /// Large glass card decoration
  static BoxDecoration get glassCardLarge => glassCard.copyWith(
    borderRadius: BorderRadius.circular(24),
  );

  /// Medium glass card decoration
  static BoxDecoration get glassCardMedium => glassCard.copyWith(
    borderRadius: BorderRadius.circular(16),
  );

  /// Small glass card decoration
  static BoxDecoration get glassCardSmall => glassCard.copyWith(
    borderRadius: BorderRadius.circular(12),
  );

  // ==================== GLASS MORPHISM CONTAINERS ====================

  /// Glass card widget with backdrop filter - Enhanced visibility
  static Widget glassContainer({
    required Widget child,
    double borderRadius = 20,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
  }) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: AppColorPalette.colorNavySurface.withValues(alpha: 0.5),  // Enhanced visibility
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: AppColorPalette.glassBorder,  // Enhanced border
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColorPalette.colorBlackPure.withValues(alpha: 0.3),  // Stronger shadow
            blurRadius: 32,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            padding: padding,
            child: child,
          ),
        ),
      ),
    );
  }

  // ==================== GLASS MORPHISM VARIANTS ====================

  /// Elevated glass decoration for modals and bottom sheets
  static BoxDecoration get glassElevated => BoxDecoration(
    color: AppColorPalette.colorNavyElevated.withValues(alpha: 0.6),
    borderRadius: BorderRadius.circular(24),
    border: Border.all(
      color: AppColorPalette.glassBorder,
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorBlackPure.withValues(alpha: 0.3),
        blurRadius: 40,
        offset: const Offset(0, 12),
      ),
    ],
  );

  /// Floating glass decoration for navigation and FABs
  static BoxDecoration get glassFloating => BoxDecoration(
    color: AppColorPalette.colorNavySurface.withValues(alpha: 0.8),
    borderRadius: BorderRadius.circular(28),
    border: Border.all(
      color: AppColorPalette.glassBorder,
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorNavyBright.withValues(alpha: 0.3),
        blurRadius: 20,
        offset: const Offset(0, 8),
      ),
    ],
  );

  // ==================== GLASS MORPHISM UTILITIES ====================

  /// Get glass color with specified opacity
  static Color glassColor([double opacity = 0.4]) {
    return AppColorPalette.colorNavySurface.withValues(alpha: opacity);
  }

  /// Get glass border color with specified opacity
  static Color glassBorderColor([double opacity = 0.1]) {
    return AppColorPalette.colorCreamWhite.withValues(alpha: opacity);
  }

  /// Create custom glass decoration
  static BoxDecoration customGlass({
    double opacity = 0.4,
    double borderRadius = 20,
    double borderOpacity = 0.1,
    double shadowOpacity = 0.2,
    double shadowBlur = 32,
    Offset shadowOffset = const Offset(0, 8),
  }) {
    return BoxDecoration(
      color: AppColorPalette.colorNavySurface.withValues(alpha: opacity),
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: AppColorPalette.colorCreamWhite.withValues(alpha: borderOpacity),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: AppColorPalette.colorBlackPure.withValues(alpha: shadowOpacity),
          blurRadius: shadowBlur,
          offset: shadowOffset,
        ),
      ],
    );
  }
}
