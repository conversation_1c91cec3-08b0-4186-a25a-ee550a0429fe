import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:glossy/glossy.dart';
import 'color_palette.dart';

/// Glass Morphism System for OpenFit
/// Implements the exact glass morphism specifications from the style guide
class GlassMorphism {
  GlassMorphism._();

  // ==================== GLASS MORPHISM DECORATIONS ====================

  /// Standard glass card decoration - Enhanced visibility
  static BoxDecoration get glassCard => BoxDecoration(
    color: AppColorPalette.colorNavySurface.withValues(alpha: 0.5),  // rgba(17, 37, 52, 0.5) - more visible
    borderRadius: BorderRadius.circular(20),
    border: Border.all(
      color: AppColorPalette.glassBorder,  // Enhanced cream white border
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorBlackPure.withValues(alpha: 0.3),  // Stronger shadow
        blurRadius: 32,
        offset: const Offset(0, 8),
      ),
    ],
  );

  /// Large glass card decoration
  static BoxDecoration get glassCardLarge => glassCard.copyWith(
    borderRadius: BorderRadius.circular(24),
  );

  /// Medium glass card decoration
  static BoxDecoration get glassCardMedium => glassCard.copyWith(
    borderRadius: BorderRadius.circular(16),
  );

  /// Small glass card decoration
  static BoxDecoration get glassCardSmall => glassCard.copyWith(
    borderRadius: BorderRadius.circular(12),
  );

  // ==================== GLASS MORPHISM CONTAINERS ====================

  /// Enhanced glass container using Glossy package with navy theme
  static Widget glassContainer({
    required Widget child,
    double borderRadius = 20,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double opacity = 0.3,
    double strengthX = 20,
    double strengthY = 20,
  }) {
    return Container(
      margin: margin,
      child: GlossyContainer(
        width: width ?? double.infinity,
        height: height ?? 200,
        borderRadius: BorderRadius.circular(borderRadius),
        strengthX: strengthX,
        strengthY: strengthY,
        opacity: opacity,
        color: AppColorPalette.colorNavySurface,
        border: Border.all(
          color: AppColorPalette.glassBorder,
          width: 1,
        ),
        child: Container(
          padding: padding ?? const EdgeInsets.all(20),
          child: child,
        ),
      ),
    );
  }

  /// Premium glass card using Glossy with enhanced navy styling
  static Widget premiumGlassCard({
    required Widget child,
    double borderRadius = 24,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    double opacity = 0.25,
    double strengthX = 25,
    double strengthY = 25,
  }) {
    return Container(
      margin: margin,
      child: GlossyContainer(
        width: width ?? double.infinity,
        height: height ?? 250,
        borderRadius: BorderRadius.circular(borderRadius),
        strengthX: strengthX,
        strengthY: strengthY,
        opacity: opacity,
        color: AppColorPalette.colorNavyElevated,
        border: Border.all(
          color: AppColorPalette.glassBorder,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColorPalette.colorNavyBright.withValues(alpha: 0.1),
            blurRadius: 40,
            offset: const Offset(0, 12),
          ),
        ],
        child: Container(
          padding: padding ?? const EdgeInsets.all(24),
          child: child,
        ),
      ),
    );
  }

  // ==================== GLOSSY ENHANCED COMPONENTS ====================

  /// Floating action button with glossy effect
  static Widget glossyFAB({
    required VoidCallback onPressed,
    required Widget child,
    double size = 56,
    Color? color,
  }) {
    return GlossyContainer(
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(size / 2),
      strengthX: 15,
      strengthY: 15,
      opacity: 0.4,
      color: color ?? AppColorPalette.colorNavyBright,
      border: Border.all(
        color: AppColorPalette.glassBorder,
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: AppColorPalette.colorNavyBright.withValues(alpha: 0.3),
          blurRadius: 20,
          offset: const Offset(0, 8),
        ),
      ],
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(size / 2),
          onTap: onPressed,
          child: Center(child: child),
        ),
      ),
    );
  }

  /// Navigation bar with glossy effect
  static Widget glossyNavBar({
    required Widget child,
    double height = 80,
    EdgeInsetsGeometry? padding,
  }) {
    return GlossyContainer(
      width: double.infinity,
      height: height,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(24),
        topRight: Radius.circular(24),
      ),
      strengthX: 20,
      strengthY: 20,
      opacity: 0.35,
      color: AppColorPalette.colorNavySurface,
      border: Border.all(
        color: AppColorPalette.glassBorder,
        width: 1,
      ),
      child: Container(
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        child: child,
      ),
    );
  }

  /// Modal/Dialog with glossy background
  static Widget glossyModal({
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.all(20),
      child: GlossyContainer(
        width: width ?? double.infinity,
        height: height ?? 400,
        borderRadius: BorderRadius.circular(28),
        strengthX: 30,
        strengthY: 30,
        opacity: 0.2,
        color: AppColorPalette.colorNavyElevated,
        border: Border.all(
          color: AppColorPalette.glassBorder,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColorPalette.colorBlackPure.withValues(alpha: 0.4),
            blurRadius: 50,
            offset: const Offset(0, 20),
          ),
        ],
        child: Container(
          padding: padding ?? const EdgeInsets.all(28),
          child: child,
        ),
      ),
    );
  }

  // ==================== GLASS MORPHISM VARIANTS ====================

  /// Elevated glass decoration for modals and bottom sheets
  static BoxDecoration get glassElevated => BoxDecoration(
    color: AppColorPalette.colorNavyElevated.withValues(alpha: 0.6),
    borderRadius: BorderRadius.circular(24),
    border: Border.all(
      color: AppColorPalette.glassBorder,
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorBlackPure.withValues(alpha: 0.3),
        blurRadius: 40,
        offset: const Offset(0, 12),
      ),
    ],
  );

  /// Floating glass decoration for navigation and FABs
  static BoxDecoration get glassFloating => BoxDecoration(
    color: AppColorPalette.colorNavySurface.withValues(alpha: 0.8),
    borderRadius: BorderRadius.circular(28),
    border: Border.all(
      color: AppColorPalette.glassBorder,
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColorPalette.colorNavyBright.withValues(alpha: 0.3),
        blurRadius: 20,
        offset: const Offset(0, 8),
      ),
    ],
  );

  // ==================== GLASS MORPHISM UTILITIES ====================

  /// Get glass color with specified opacity
  static Color glassColor([double opacity = 0.4]) {
    return AppColorPalette.colorNavySurface.withValues(alpha: opacity);
  }

  /// Get glass border color with specified opacity
  static Color glassBorderColor([double opacity = 0.1]) {
    return AppColorPalette.colorCreamWhite.withValues(alpha: opacity);
  }

  /// Create custom glass decoration
  static BoxDecoration customGlass({
    double opacity = 0.4,
    double borderRadius = 20,
    double borderOpacity = 0.1,
    double shadowOpacity = 0.2,
    double shadowBlur = 32,
    Offset shadowOffset = const Offset(0, 8),
  }) {
    return BoxDecoration(
      color: AppColorPalette.colorNavySurface.withValues(alpha: opacity),
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: AppColorPalette.colorCreamWhite.withValues(alpha: borderOpacity),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: AppColorPalette.colorBlackPure.withValues(alpha: shadowOpacity),
          blurRadius: shadowBlur,
          offset: shadowOffset,
        ),
      ],
    );
  }
}
