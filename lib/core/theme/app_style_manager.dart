import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'global_theme_config.dart';
import 'color_palette.dart';
import 'typography.dart';
import 'glass_morphism.dart';
import 'animations.dart';
import '../../shared/widgets/loading/app_loading_components.dart';

/// Centralized style manager for the entire app
/// This is your ONE PLACE to modify app styles globally for both dark/light themes and iOS/Android
class AppStyleManager {
  AppStyleManager._();

  /// Get the current theme-aware style manager instance
  static AppStyleManager of(BuildContext context, WidgetRef ref) {
    return AppStyleManager._();
  }

  // ==================== COLORS ====================
  
  /// Primary brand colors - Navy theme
  static Color primaryColor(BuildContext context) {
    return AppColorPalette.accentPrimary; // Navy blue
  }

  static Color primaryColorLight(BuildContext context) {
    return AppColorPalette.accentBright; // Lighter navy
  }

  static Color primaryColorDark(BuildContext context) {
    return AppColorPalette.accentBlueDark; // Darker navy
  }

  /// Background colors - Navy theme backgrounds
  static Color backgroundColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColorPalette.bgPrimary : AppColorPalette.bgSurface;
  }

  static Color surfaceColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColorPalette.bgSurface : AppColorPalette.bgElevated;
  }

  static Color cardColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? AppColorPalette.bgSurface : AppColorPalette.bgElevated;
  }

  /// Text colors - Navy theme text
  static Color textPrimary(BuildContext context) {
    return AppColorPalette.textPrimary; // Always white for navy theme
  }

  static Color textSecondary(BuildContext context) {
    return AppColorPalette.textSecondary; // Light blue-gray
  }

  static Color textTertiary(BuildContext context) {
    return AppColorPalette.textTertiary; // Muted blue-gray
  }

  // ==================== GRADIENTS ====================

  /// Primary gradient - Navy theme gradient
  static LinearGradient primaryGradient(BuildContext context) {
    return AppColorPalette.primaryGradient;
  }

  /// Accent gradient - Orange accent gradient for special actions
  static LinearGradient accentGradient(BuildContext context) {
    return AppColorPalette.accentGradient;
  }

  // ==================== BUTTON SYSTEM (EXACT SPECIFICATIONS) ====================

  /// Primary Button - Flat Tesla design (Navy gradient)
  static ButtonStyle primaryButton(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.transparent,
      foregroundColor: AppColorPalette.colorCreamWhite,
      elevation: 0, // Completely flat
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      minimumSize: const Size(0, 48),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8), // Minimal rounding for flat look
      ),
      textStyle: AppTypography.buttonTextStyle(color: AppColorPalette.colorCreamWhite),
    ).copyWith(
      backgroundColor: WidgetStateProperty.resolveWith((states) {
        return Colors.transparent; // Gradient will be applied via decoration
      }),
    );
  }

  /// Energy Button - ONLY for workout start buttons (Orange gradient)
  static ButtonStyle energyButton(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.transparent,
      foregroundColor: AppColorPalette.colorCreamWhite,
      elevation: 0,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      minimumSize: const Size(0, 48),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      textStyle: AppTypography.buttonTextStyle(color: AppColorPalette.colorCreamWhite),
    );
  }

  /// Ghost Button - Flat Tesla style (Transparent with minimal border)
  static ButtonStyle ghostButton(BuildContext context) {
    return OutlinedButton.styleFrom(
      backgroundColor: Colors.transparent,
      foregroundColor: AppColorPalette.colorCreamWhite,
      side: BorderSide(
        color: AppColorPalette.colorCreamWhite.withValues(alpha: 0.2), // More subtle border
        width: 1, // Thinner border for flat look
      ),
      padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 14),
      minimumSize: const Size(0, 48),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8), // Minimal rounding
      ),
      textStyle: AppTypography.buttonTextStyle(color: AppColorPalette.colorCreamWhite),
    );
  }

  /// Flat Primary Button Container - Tesla style
  static Widget primaryButtonContainer({
    required Widget child,
    VoidCallback? onPressed,
    bool isEnabled = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: isEnabled
          ? AppColorPalette.primaryGradient
          : LinearGradient(
              colors: AppColorPalette.primaryGradient.colors
                  .map((color) => color.withValues(alpha: 0.5))
                  .toList(),
              begin: AppColorPalette.primaryGradient.begin,
              end: AppColorPalette.primaryGradient.end,
            ),
        borderRadius: BorderRadius.circular(8), // Minimal rounding for flat look
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: isEnabled ? onPressed : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            constraints: const BoxConstraints(minHeight: 48),
            child: Center(child: child),
          ),
        ),
      ),
    );
  }

  /// Flat Energy Button Container - Tesla style (orange gradient for CTA only)
  static Widget energyButtonContainer({
    required Widget child,
    VoidCallback? onPressed,
    bool isEnabled = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: isEnabled
          ? AppColorPalette.energyGradient
          : LinearGradient(
              colors: AppColorPalette.energyGradient.colors
                  .map((color) => color.withValues(alpha: 0.5))
                  .toList(),
              begin: AppColorPalette.energyGradient.begin,
              end: AppColorPalette.energyGradient.end,
            ),
        borderRadius: BorderRadius.circular(8), // Minimal rounding for flat look
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: isEnabled ? onPressed : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            constraints: const BoxConstraints(minHeight: 48),
            child: Center(child: child),
          ),
        ),
      ),
    );
  }

  // ==================== LEGACY BUTTON SUPPORT ====================

  /// Secondary button style - use this for secondary actions
  static ButtonStyle secondaryButton(BuildContext context) {
    return ghostButton(context);
  }

  /// Flat danger button style - Tesla design for destructive actions
  static ButtonStyle dangerButton(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: AppColorPalette.error,
      foregroundColor: AppColorPalette.colorCreamWhite,
      elevation: 0, // Completely flat
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      minimumSize: const Size(0, 48),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8), // Minimal rounding
      ),
      textStyle: AppTypography.buttonTextStyle(),
    );
  }

  /// Text button style - use this for subtle actions
  static ButtonStyle textButton(BuildContext context) {
    return TextButton.styleFrom(
      foregroundColor: AppColorPalette.colorNavyBright,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      textStyle: AppTypography.buttonTextStyle(color: AppColorPalette.colorNavyBright),
    );
  }

  // ==================== INPUTS ====================
  
  /// Standard input decoration - use this for all text fields
  static InputDecoration inputDecoration(BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isError = false,
  }) {
    return GlobalThemeConfig.inputDecoration(
      context,
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      isError: isError,
    );
  }

  // ==================== GLASS MORPHISM CARD SYSTEM ====================

  /// Standard glass card decoration - REQUIRED for all cards
  static BoxDecoration cardDecoration(BuildContext context, {
    double? elevation,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    return GlassMorphism.glassCard.copyWith(
      borderRadius: borderRadius ?? BorderRadius.circular(20),
    );
  }

  /// Large glass card decoration
  static BoxDecoration cardDecorationLarge(BuildContext context) {
    return GlassMorphism.glassCardLarge;
  }

  /// Medium glass card decoration
  static BoxDecoration cardDecorationMedium(BuildContext context) {
    return GlassMorphism.glassCardMedium;
  }

  /// Small glass card decoration
  static BoxDecoration cardDecorationSmall(BuildContext context) {
    return GlassMorphism.glassCardSmall;
  }

  /// Elevated glass card decoration - for modals and bottom sheets
  static BoxDecoration elevatedCardDecoration(BuildContext context) {
    return GlassMorphism.glassElevated;
  }

  /// Floating glass decoration - for navigation and FABs
  static BoxDecoration floatingCardDecoration(BuildContext context) {
    return GlassMorphism.glassFloating;
  }

  /// Glass container widget with backdrop filter
  static Widget glassContainer({
    required Widget child,
    double borderRadius = 20,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
  }) {
    return GlassMorphism.glassContainer(
      child: child,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
    );
  }

  /// Workout card decoration - glass morphism with navy gradient
  static BoxDecoration workoutCardDecoration(BuildContext context) {
    return GlassMorphism.glassCard.copyWith(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColorPalette.colorNavySurface.withValues(alpha: 0.6),
          AppColorPalette.colorNavyElevated.withValues(alpha: 0.4),
        ],
      ),
    );
  }

  // ==================== LEGACY CARD SUPPORT ====================

  /// Legacy card decoration for backward compatibility
  static BoxDecoration legacyCardDecoration(BuildContext context, {
    double? elevation,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    return GlobalThemeConfig.cardDecoration(
      context,
      elevation: elevation,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
    );
  }

  // ==================== LOADING COMPONENTS ====================
  
  /// Primary loading indicator - use this for main loading states
  static Widget primaryLoader({
    double size = 32.0,
    String? message,
  }) {
    return AppLoadingComponents.primaryLoader(
      size: size,
      message: message,
    );
  }

  /// Button loading indicator - use this inside buttons
  static Widget buttonLoader({Color? color}) {
    return AppLoadingComponents.buttonLoader(color: color);
  }

  /// Workout card skeleton - use this while loading workout lists
  static Widget workoutCardSkeleton() {
    return AppLoadingComponents.workoutCardSkeleton();
  }

  /// Exercise list skeleton - use this while loading exercise lists
  static Widget exerciseListSkeleton({int itemCount = 3}) {
    return AppLoadingComponents.exerciseListSkeleton(itemCount: itemCount);
  }

  /// Full screen loader - use this for full screen loading states
  static Widget fullScreenLoader({
    String? message,
    bool canDismiss = false,
  }) {
    return AppLoadingComponents.fullScreenLoader(
      message: message,
      canDismiss: canDismiss,
    );
  }

  // ==================== SNACKBARS & NOTIFICATIONS ====================
  
  /// Success snackbar - use this for success messages
  static SnackBar successSnackBar(String message, {VoidCallback? action, String? actionLabel}) {
    return GlobalThemeConfig.styledSnackBar(
      message,
      type: SnackBarType.success,
      action: action,
      actionLabel: actionLabel,
    );
  }

  /// Error snackbar - use this for error messages
  static SnackBar errorSnackBar(String message, {VoidCallback? action, String? actionLabel}) {
    return GlobalThemeConfig.styledSnackBar(
      message,
      type: SnackBarType.error,
      action: action,
      actionLabel: actionLabel,
    );
  }

  /// Warning snackbar - use this for warning messages
  static SnackBar warningSnackBar(String message, {VoidCallback? action, String? actionLabel}) {
    return GlobalThemeConfig.styledSnackBar(
      message,
      type: SnackBarType.warning,
      action: action,
      actionLabel: actionLabel,
    );
  }

  /// Info snackbar - use this for info messages
  static SnackBar infoSnackBar(String message, {VoidCallback? action, String? actionLabel}) {
    return GlobalThemeConfig.styledSnackBar(
      message,
      type: SnackBarType.info,
      action: action,
      actionLabel: actionLabel,
    );
  }

  // ==================== 8PT GRID SPACING SYSTEM ====================

  /// Micro spacing - 4px
  static const double spaceXs = 4.0;
  static EdgeInsets get paddingXs => const EdgeInsets.all(spaceXs);

  /// Small spacing - 8px
  static const double spaceSm = 8.0;
  static EdgeInsets get paddingSm => const EdgeInsets.all(spaceSm);

  /// Standard spacing - 16px
  static const double spaceMd = 16.0;
  static EdgeInsets get paddingMd => const EdgeInsets.all(spaceMd);

  /// Large spacing - 24px
  static const double spaceLg = 24.0;
  static EdgeInsets get paddingLg => const EdgeInsets.all(spaceLg);

  /// Extra large spacing - 32px
  static const double spaceXl = 32.0;
  static EdgeInsets get paddingXl => const EdgeInsets.all(spaceXl);

  /// Section spacing - 48px
  static const double spaceXxl = 48.0;
  static EdgeInsets get paddingXxl => const EdgeInsets.all(spaceXxl);

  // ==================== RESPONSIVE SCREEN MARGINS ====================

  /// Mobile margins (320px-767px) - 20px horizontal
  static EdgeInsets get mobilePadding => const EdgeInsets.symmetric(horizontal: 20.0);

  /// Tablet margins (768px-1023px) - 32px horizontal
  static EdgeInsets get tabletPadding => const EdgeInsets.symmetric(horizontal: 32.0);

  /// Desktop margins (1024px+) - 48px horizontal
  static EdgeInsets get desktopPadding => const EdgeInsets.symmetric(horizontal: 48.0);

  // ==================== LEGACY SPACING SUPPORT ====================

  /// Standard padding - use this for consistent spacing
  static EdgeInsets get standardPadding => paddingMd;

  /// Small padding - use this for tight spacing
  static EdgeInsets get smallPadding => paddingSm;

  /// Large padding - use this for loose spacing
  static EdgeInsets get largePadding => paddingLg;

  /// Standard border radius - flat Tesla style with minimal rounding
  static BorderRadius get standardBorderRadius => BorderRadius.circular(8);

  /// Large border radius - slightly more rounded but still flat
  static BorderRadius get largeBorderRadius => BorderRadius.circular(12);

  // ==================== ANIMATION SYSTEM (EXACT SPECIFICATIONS) ====================

  /// Fast animation duration - 200ms
  static Duration get fastAnimation => AppAnimations.fast;

  /// Normal animation duration - 300ms
  static Duration get normalAnimation => AppAnimations.normal;

  /// Slow animation duration - 400ms
  static Duration get slowAnimation => AppAnimations.slow;

  /// Standard easing curve - cubic-bezier(0.4, 0, 0.2, 1)
  static Curve get standardEasing => AppAnimations.standard;

  /// Bounce easing curve - cubic-bezier(0.68, -0.55, 0.265, 1.55)
  static Curve get bounceEasing => AppAnimations.bounce;

  // ==================== LEGACY ANIMATION SUPPORT ====================

  /// Medium animation duration - use this for standard transitions
  static Duration get mediumAnimation => normalAnimation;

  // ==================== HELPER METHODS ====================
  
  /// Check if current theme is dark
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Check if current platform is iOS
  static bool get isIOS => GlobalThemeConfig.isIOS;

  /// Check if current platform is Android
  static bool get isAndroid => GlobalThemeConfig.isAndroid;

  /// Show styled snackbar with context
  static void showSnackBar(BuildContext context, SnackBar snackBar) {
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /// Show success message
  static void showSuccess(BuildContext context, String message, {VoidCallback? action, String? actionLabel}) {
    showSnackBar(context, successSnackBar(message, action: action, actionLabel: actionLabel));
  }

  /// Show error message
  static void showError(BuildContext context, String message, {VoidCallback? action, String? actionLabel}) {
    showSnackBar(context, errorSnackBar(message, action: action, actionLabel: actionLabel));
  }

  /// Show warning message
  static void showWarning(BuildContext context, String message, {VoidCallback? action, String? actionLabel}) {
    showSnackBar(context, warningSnackBar(message, action: action, actionLabel: actionLabel));
  }

  /// Show info message
  static void showInfo(BuildContext context, String message, {VoidCallback? action, String? actionLabel}) {
    showSnackBar(context, infoSnackBar(message, action: action, actionLabel: actionLabel));
  }
}

/// Extension to make AppStyleManager easily accessible
extension AppStyleManagerExtension on BuildContext {
  /// Quick access to AppStyleManager
  AppStyleManager get styles => AppStyleManager.of(this, ProviderScope.containerOf(this) as WidgetRef);
}
