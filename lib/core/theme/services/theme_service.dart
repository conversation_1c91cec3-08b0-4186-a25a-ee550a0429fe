import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme_data.dart';
import '../theme_config.dart';

/// Service layer for theme management and operations
abstract class ThemeService {
  /// Get current theme mode
  Future<AppThemeMode> getThemeMode();
  
  /// Set theme mode
  Future<void> setThemeMode(AppThemeMode mode);
  
  /// Get theme settings
  Future<AppThemeSettings> getThemeSettings();
  
  /// Set theme settings
  Future<void> setThemeSettings(AppThemeSettings settings);
  
  /// Clear all theme data
  Future<void> clearThemeData();
  
  /// Export theme configuration
  Future<Map<String, dynamic>> exportThemeConfig();
  
  /// Import theme configuration
  Future<void> importThemeConfig(Map<String, dynamic> config);
  
  /// Update system UI overlay
  void updateSystemUIOverlay(bool isDarkMode);
  
  /// Get theme variant
  Future<ThemeVariant> getThemeVariant();
  
  /// Set theme variant
  Future<void> setThemeVariant(ThemeVariant variant);
}

/// Implementation of theme service
class ThemeServiceImpl implements ThemeService {
  final ThemeRepository _repository;
  
  ThemeServiceImpl(this._repository);

  @override
  Future<AppThemeMode> getThemeMode() async {
    try {
      return _repository.getThemeMode();
    } catch (e) {
      // Fallback to system theme if error occurs
      return AppThemeMode.system;
    }
  }

  @override
  Future<void> setThemeMode(AppThemeMode mode) async {
    try {
      await _repository.setThemeMode(mode);
      
      // Update system UI overlay if needed
      final isSystemDark = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
      final isDarkMode = mode == AppThemeMode.dark || 
                        (mode == AppThemeMode.system && isSystemDark);
      
      updateSystemUIOverlay(isDarkMode);
    } catch (e) {
      throw ThemeServiceException('Failed to set theme mode: $e');
    }
  }

  @override
  Future<AppThemeSettings> getThemeSettings() async {
    try {
      return await _repository.getThemeSettings() ?? ThemeConfig.defaultSettings;
    } catch (e) {
      return ThemeConfig.defaultSettings;
    }
  }

  @override
  Future<void> setThemeSettings(AppThemeSettings settings) async {
    try {
      await _repository.setThemeSettings(settings);
    } catch (e) {
      throw ThemeServiceException('Failed to set theme settings: $e');
    }
  }

  @override
  Future<void> clearThemeData() async {
    try {
      await _repository.clearThemePreferences();
    } catch (e) {
      throw ThemeServiceException('Failed to clear theme data: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> exportThemeConfig() async {
    try {
      final themeMode = await getThemeMode();
      final settings = await getThemeSettings();
      final variant = await getThemeVariant();
      
      return {
        'version': ThemeConfig.version,
        'themeMode': themeMode.name,
        'settings': {
          'useMaterial3': settings.useMaterial3,
          'enableAnimations': settings.enableAnimations,
          'enableHapticFeedback': settings.enableHapticFeedback,
          'enableSystemUIOverlay': settings.enableSystemUIOverlay,
          'defaultAnimationDuration': settings.defaultAnimationDuration.inMilliseconds,
        },
        'variant': variant.name,
        'exportedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw ThemeServiceException('Failed to export theme config: $e');
    }
  }

  @override
  Future<void> importThemeConfig(Map<String, dynamic> config) async {
    try {
      // Validate config version
      final version = config['version'] as String?;
      if (version != ThemeConfig.version) {
        throw const ThemeServiceException('Incompatible theme config version');
      }
      
      // Import theme mode
      final themeModeString = config['themeMode'] as String?;
      if (themeModeString != null) {
        final themeMode = AppThemeMode.values.firstWhere(
          (mode) => mode.name == themeModeString,
          orElse: () => AppThemeMode.system,
        );
        await setThemeMode(themeMode);
      }
      
      // Import settings
      final settingsMap = config['settings'] as Map<String, dynamic>?;
      if (settingsMap != null) {
        final settings = AppThemeSettings(
          useMaterial3: settingsMap['useMaterial3'] as bool? ?? true,
          enableAnimations: settingsMap['enableAnimations'] as bool? ?? true,
          enableHapticFeedback: settingsMap['enableHapticFeedback'] as bool? ?? true,
          enableSystemUIOverlay: settingsMap['enableSystemUIOverlay'] as bool? ?? true,
          defaultAnimationDuration: Duration(
            milliseconds: settingsMap['defaultAnimationDuration'] as int? ?? 300,
          ),
        );
        await setThemeSettings(settings);
      }
      
      // Import variant
      final variantString = config['variant'] as String?;
      if (variantString != null) {
        final variant = ThemeVariant.values.firstWhere(
          (v) => v.name == variantString,
          orElse: () => ThemeVariant.standard,
        );
        await setThemeVariant(variant);
      }
    } catch (e) {
      throw ThemeServiceException('Failed to import theme config: $e');
    }
  }

  @override
  void updateSystemUIOverlay(bool isDarkMode) {
    try {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
          statusBarBrightness: isDarkMode ? Brightness.dark : Brightness.light,
          systemNavigationBarColor: isDarkMode 
              ? const Color(0xFF171717) 
              : Colors.white,
          systemNavigationBarIconBrightness: isDarkMode 
              ? Brightness.light 
              : Brightness.dark,
          systemNavigationBarDividerColor: Colors.transparent,
        ),
      );
    } catch (e) {
      // Silently fail on system UI overlay errors
      debugPrint('Failed to update system UI overlay: $e');
    }
  }

  @override
  Future<ThemeVariant> getThemeVariant() async {
    try {
      return await _repository.getThemeVariant() ?? ThemeVariant.standard;
    } catch (e) {
      return ThemeVariant.standard;
    }
  }

  @override
  Future<void> setThemeVariant(ThemeVariant variant) async {
    try {
      await _repository.setThemeVariant(variant);
    } catch (e) {
      throw ThemeServiceException('Failed to set theme variant: $e');
    }
  }
}

/// Theme service exception
class ThemeServiceException implements Exception {
  final String message;
  
  const ThemeServiceException(this.message);
  
  @override
  String toString() => 'ThemeServiceException: $message';
}

/// Extended theme repository interface
abstract class ThemeRepository {
  AppThemeMode getThemeMode();
  Future<void> setThemeMode(AppThemeMode mode);
  Future<AppThemeSettings?> getThemeSettings();
  Future<void> setThemeSettings(AppThemeSettings settings);
  Future<void> clearThemePreferences();
  Future<ThemeVariant?> getThemeVariant();
  Future<void> setThemeVariant(ThemeVariant variant);
}
