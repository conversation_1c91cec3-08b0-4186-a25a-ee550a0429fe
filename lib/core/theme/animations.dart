import 'package:flutter/material.dart';

/// Animation System for OpenFit
/// Implements exact timing and easing specifications from the style guide
class AppAnimations {
  AppAnimations._();

  // ==================== ANIMATION TIMING (EXACT SPECIFICATIONS) ====================

  /// Standard timing durations
  static const Duration fast = Duration(milliseconds: 200);      // --animation-fast
  static const Duration normal = Duration(milliseconds: 300);    // --animation-normal
  static const Duration slow = Duration(milliseconds: 400);      // --animation-slow

  // ==================== EASING CURVES (EXACT SPECIFICATIONS) ====================

  /// Required easing curves
  static const Curve standard = Cubic(0.4, 0, 0.2, 1);         // --easing-standard
  static const Curve bounce = Cubic(0.68, -0.55, 0.265, 1.55); // --easing-bounce

  /// Additional easing curves for enhanced interactions
  static const Curve easeOut = Curves.easeOut;
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve elasticOut = Curves.elasticOut;

  // ==================== TOUCH FEEDBACK ANIMATIONS ====================

  /// Touch feedback scale animation
  static AnimationController createTouchController(TickerProvider vsync) {
    return AnimationController(
      duration: fast,
      vsync: vsync,
    );
  }

  /// Touch feedback scale animation
  static Animation<double> createTouchScale(AnimationController controller) {
    return Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: standard,
    ));
  }

  /// Touch feedback opacity animation
  static Animation<double> createTouchOpacity(AnimationController controller) {
    return Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: standard,
    ));
  }

  // ==================== HOVER ANIMATIONS ====================

  /// Hover elevation animation
  static Animation<double> createHoverElevation(AnimationController controller) {
    return Tween<double>(
      begin: 0.0,
      end: -2.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: standard,
    ));
  }

  /// Hover shadow animation
  static Animation<double> createHoverShadow(AnimationController controller) {
    return Tween<double>(
      begin: 8.0,
      end: 12.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: standard,
    ));
  }

  // ==================== PAGE TRANSITIONS ====================

  /// Slide transition from right
  static Widget slideFromRight(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: standard,
      )),
      child: child,
    );
  }

  /// Fade transition
  static Widget fadeTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return FadeTransition(
      opacity: CurvedAnimation(
        parent: animation,
        curve: standard,
      ),
      child: child,
    );
  }

  /// Scale transition with bounce
  static Widget scaleTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return ScaleTransition(
      scale: CurvedAnimation(
        parent: animation,
        curve: bounce,
      ),
      child: child,
    );
  }

  // ==================== LOADING ANIMATIONS ====================

  /// Pulse animation for loading states
  static Animation<double> createPulse(AnimationController controller) {
    return Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ));
  }

  /// Rotation animation for spinners
  static Animation<double> createRotation(AnimationController controller) {
    return Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.linear,
    ));
  }

  // ==================== UTILITY METHODS ====================

  /// Create a standard animation controller
  static AnimationController createController({
    required TickerProvider vsync,
    Duration duration = normal,
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
    );
  }

  /// Create a repeating animation controller
  static AnimationController createRepeatingController({
    required TickerProvider vsync,
    Duration duration = normal,
  }) {
    final controller = AnimationController(
      duration: duration,
      vsync: vsync,
    );
    controller.repeat();
    return controller;
  }

  /// Create a reversing animation controller
  static AnimationController createReversingController({
    required TickerProvider vsync,
    Duration duration = normal,
  }) {
    final controller = AnimationController(
      duration: duration,
      vsync: vsync,
    );
    controller.repeat(reverse: true);
    return controller;
  }
}

/// Animation mixin for easy integration
mixin AppAnimationMixin<T extends StatefulWidget> on State<T>, TickerProviderStateMixin<T> {
  late AnimationController _touchController;
  late Animation<double> _touchScale;
  late Animation<double> _touchOpacity;

  @override
  void initState() {
    super.initState();
    _touchController = AppAnimations.createTouchController(this);
    _touchScale = AppAnimations.createTouchScale(_touchController);
    _touchOpacity = AppAnimations.createTouchOpacity(_touchController);
  }

  @override
  void dispose() {
    _touchController.dispose();
    super.dispose();
  }

  /// Animate touch down
  void animateTouchDown() {
    _touchController.forward();
  }

  /// Animate touch up
  void animateTouchUp() {
    _touchController.reverse();
  }

  /// Get touch scale animation
  Animation<double> get touchScale => _touchScale;

  /// Get touch opacity animation
  Animation<double> get touchOpacity => _touchOpacity;
}
