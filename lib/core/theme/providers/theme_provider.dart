import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/constants/app_constants.dart';
import '../theme_data.dart';
import '../theme_config.dart';
import '../services/theme_service.dart';
import '../global_theme_config.dart';
import '../color_palette.dart';

/// Provider for SharedPreferences
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be overridden');
});

/// Enhanced theme repository implementation
class ThemeRepositoryImpl implements ThemeRepository {
  final SharedPreferences _prefs;

  ThemeRepositoryImpl(this._prefs);

  static const String _themeSettingsKey = 'theme_settings';
  static const String _themeVariantKey = 'theme_variant';

  @override
  AppThemeMode getThemeMode() {
    final themeModeString = _prefs.getString(AppConstants.themePreferenceKey);
    if (themeModeString == null) {
      return AppThemeMode.system;
    }

    return AppThemeMode.values.firstWhere(
      (mode) => mode.name == themeModeString,
      orElse: () => AppThemeMode.system,
    );
  }

  @override
  Future<void> setThemeMode(AppThemeMode mode) async {
    await _prefs.setString(AppConstants.themePreferenceKey, mode.name);
  }

  @override
  Future<AppThemeSettings?> getThemeSettings() async {
    final settingsJson = _prefs.getString(_themeSettingsKey);
    if (settingsJson == null) return null;

    try {
      final Map<String, dynamic> data = Map<String, dynamic>.from(
        Uri.splitQueryString(settingsJson),
      );

      return AppThemeSettings(
        useMaterial3: data['useMaterial3'] == 'true',
        enableAnimations: data['enableAnimations'] == 'true',
        enableHapticFeedback: data['enableHapticFeedback'] == 'true',
        enableSystemUIOverlay: data['enableSystemUIOverlay'] == 'true',
        defaultAnimationDuration: Duration(
          milliseconds: int.tryParse(data['defaultAnimationDuration'] ?? '300') ?? 300,
        ),
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> setThemeSettings(AppThemeSettings settings) async {
    final data = {
      'useMaterial3': settings.useMaterial3.toString(),
      'enableAnimations': settings.enableAnimations.toString(),
      'enableHapticFeedback': settings.enableHapticFeedback.toString(),
      'enableSystemUIOverlay': settings.enableSystemUIOverlay.toString(),
      'defaultAnimationDuration': settings.defaultAnimationDuration.inMilliseconds.toString(),
    };

    final queryString = data.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');

    await _prefs.setString(_themeSettingsKey, queryString);
  }

  @override
  Future<void> clearThemePreferences() async {
    await Future.wait([
      _prefs.remove(AppConstants.themePreferenceKey),
      _prefs.remove(_themeSettingsKey),
      _prefs.remove(_themeVariantKey),
    ]);
  }

  @override
  Future<ThemeVariant?> getThemeVariant() async {
    final variantString = _prefs.getString(_themeVariantKey);
    if (variantString == null) return null;

    return ThemeVariant.values.firstWhere(
      (variant) => variant.name == variantString,
      orElse: () => ThemeVariant.standard,
    );
  }

  @override
  Future<void> setThemeVariant(ThemeVariant variant) async {
    await _prefs.setString(_themeVariantKey, variant.name);
  }
}

/// Provider for theme repository
final themeRepositoryProvider = Provider<ThemeRepository>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return ThemeRepositoryImpl(prefs);
});

/// Provider for theme service
final themeServiceProvider = Provider<ThemeService>((ref) {
  final repository = ref.watch(themeRepositoryProvider);
  return ThemeServiceImpl(repository);
});

/// Theme notifier for managing theme state
class ThemeNotifier extends StateNotifier<AppThemeData> {
  final ThemeRepository _repository;
  
  ThemeNotifier(this._repository) : super(_getInitialTheme(_repository)) {
    _initializeTheme();
  }
  
  static AppThemeData _getInitialTheme(ThemeRepository repository) {
    final savedMode = repository.getThemeMode();
    final isSystemDark = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
    
    return AppThemeData(
      mode: savedMode,
      isDarkMode: savedMode == AppThemeMode.dark || 
                  (savedMode == AppThemeMode.system && isSystemDark),
      useSystemTheme: savedMode == AppThemeMode.system,
    );
  }
  
  void _initializeTheme() {
    // Listen to system brightness changes when using system theme
    if (state.useSystemTheme) {
      _updateSystemTheme();
    }
  }
  
  void _updateSystemTheme() {
    if (state.useSystemTheme) {
      final isSystemDark = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
      if (state.isDarkMode != isSystemDark) {
        state = state.copyWith(isDarkMode: isSystemDark);
      }
    }
  }
  
  /// Set theme mode
  Future<void> setThemeMode(AppThemeMode mode) async {
    await _repository.setThemeMode(mode);
    
    final isSystemDark = WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
    final isDarkMode = mode == AppThemeMode.dark || 
                      (mode == AppThemeMode.system && isSystemDark);
    
    state = AppThemeData(
      mode: mode,
      isDarkMode: isDarkMode,
      useSystemTheme: mode == AppThemeMode.system,
    );
    
    // Update system UI overlay style
    _updateSystemUIOverlayStyle();
  }
  
  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    final newMode = state.isDarkMode ? AppThemeMode.light : AppThemeMode.dark;
    await setThemeMode(newMode);
  }
  
  /// Update system UI overlay style based on current theme
  void _updateSystemUIOverlayStyle() {
    final isDark = state.isDarkMode;

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        systemNavigationBarColor: isDark ? AppColorPalette.colorNavySurface : AppColorPalette.colorCreamWhite,
        systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarDividerColor: Colors.transparent,
      ),
    );
  }

  /// Get platform-specific button style using global theme config
  ButtonStyle getPrimaryButtonStyle(BuildContext context) {
    return GlobalThemeConfig.primaryButtonStyle(context);
  }

  /// Get platform-specific secondary button style
  ButtonStyle getSecondaryButtonStyle(BuildContext context) {
    return GlobalThemeConfig.secondaryButtonStyle(context);
  }

  /// Get platform-specific input decoration
  InputDecoration getInputDecoration(BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isError = false,
  }) {
    return GlobalThemeConfig.inputDecoration(
      context,
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      isError: isError,
    );
  }

  /// Get platform-specific card decoration
  BoxDecoration getCardDecoration(BuildContext context, {
    double? elevation,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    return GlobalThemeConfig.cardDecoration(
      context,
      elevation: elevation,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
    );
  }
  
  /// Handle system brightness changes
  void handleSystemBrightnessChange() {
    if (state.useSystemTheme) {
      _updateSystemTheme();
    }
  }
  
  /// Reset theme to system default
  Future<void> resetToSystemTheme() async {
    await setThemeMode(AppThemeMode.system);
  }
}

/// Provider for theme state
final themeProvider = StateNotifierProvider<ThemeNotifier, AppThemeData>((ref) {
  final repository = ref.watch(themeRepositoryProvider);
  return ThemeNotifier(repository);
});

/// Provider for current theme mode
final currentThemeModeProvider = Provider<AppThemeMode>((ref) {
  return ref.watch(themeProvider).mode;
});

/// Provider for checking if dark mode is active
final isDarkModeProvider = Provider<bool>((ref) {
  return ref.watch(themeProvider).isDarkMode;
});

/// Provider for checking if system theme is being used
final useSystemThemeProvider = Provider<bool>((ref) {
  return ref.watch(themeProvider).useSystemTheme;
});

/// Provider for theme mode options
final themeModeOptionsProvider = Provider<List<AppThemeMode>>((ref) {
  return AppThemeMode.values;
});
