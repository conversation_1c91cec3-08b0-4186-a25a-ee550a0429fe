import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_profile.dart';
import '../../../auth/domain/providers/auth_provider.dart';
import '../../../onboarding/domain/providers/onboarding_provider.dart';

/// Provider for user profile
final profileProvider = FutureProvider<UserProfile?>((ref) async {
  final authState = ref.watch(authStateProvider);
  final user = authState.valueOrNull?.session?.user;
  
  if (user == null) {
    return null;
  }
  
  final repository = ref.read(onboardingRepositoryProvider);
  return repository.getUserProfile(user.id);
});

/// Provider for current user ID
final currentUserIdProvider = Provider<String?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.valueOrNull?.session?.user.id;
});

/// Provider for user display name
final userDisplayNameProvider = Provider<String?>((ref) {
  final profile = ref.watch(profileProvider);
  return profile.when(
    data: (profile) => profile?.displayName,
    loading: () => null,
    error: (error, stack) => null,
  );
});

/// Provider for onboarding completion status
final onboardingCompletedProvider = FutureProvider<bool>((ref) async {
  final userId = ref.watch(currentUserIdProvider);
  
  if (userId == null) {
    return false;
  }
  
  final repository = ref.read(onboardingRepositoryProvider);
  return repository.hasCompletedOnboarding(userId);
});
