import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:typed_data';
import '../../../../shared/services/openai_realtime_webrtc_service.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../core/theme/color_palette.dart';

final openAIRealtimeProvider = ChangeNotifierProvider<OpenAIRealtimeService>((ref) {
  return OpenAIRealtimeService();
});

class TestScreenV2 extends ConsumerStatefulWidget {
  const TestScreenV2({super.key});

  @override
  ConsumerState<TestScreenV2> createState() => _TestScreenV2State();
}

class _TestScreenV2State extends ConsumerState<TestScreenV2> {
  final AudioRecorder _audioRecorder = AudioRecorder();
  bool _isConversationActive = false;
  String? _errorMessage;
  final List<Map<String, String>> _conversationHistory = [];
  final ScrollController _scrollController = ScrollController();
  Stream<List<int>>? _audioStream;

  @override
  void initState() {
    super.initState();
    _initializeService();
    _setupConversationListeners();
  }

  void _setupConversationListeners() {
    final service = ref.read(openAIRealtimeProvider);
    
    // Listen to transcripts
    service.transcriptStream.listen((transcript) {
      if (transcript.isNotEmpty && _isConversationActive) {
        setState(() {
          // Add or update user message
          if (_conversationHistory.isNotEmpty && 
              _conversationHistory.last['role'] == 'user' &&
              _conversationHistory.last['status'] == 'speaking') {
            _conversationHistory.last['message'] = transcript;
          } else {
            _conversationHistory.add({
              'role': 'user',
              'message': transcript,
              'status': 'speaking'
            });
          }
        });
        _scrollToBottom();
      }
    });
    
    // Listen to responses
    service.responseStream.listen((response) {
      if (response.isNotEmpty && _isConversationActive) {
        setState(() {
          // Mark last user message as complete
          if (_conversationHistory.isNotEmpty && 
              _conversationHistory.last['role'] == 'user') {
            _conversationHistory.last['status'] = 'complete';
          }
          
          // Add assistant response
          _conversationHistory.add({
            'role': 'assistant',
            'message': response,
            'status': 'complete'
          });
        });
        _scrollToBottom();
      }
    });
  }
  
  void _scrollToBottom() {
    if (!mounted) return;
    
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted && _scrollController.hasClients && _scrollController.position.hasContentDimensions) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _initializeService() async {
    try {
      final service = ref.read(openAIRealtimeProvider);
      await service.initialize();
      await service.connect();
      
      setState(() {
        _errorMessage = null;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to connect: ${e.toString()}';
      });
    }
  }

  Future<void> _checkPermissions() async {
    final status = await Permission.microphone.request();
    if (!status.isGranted) {
      setState(() {
        _errorMessage = 'Microphone permission denied';
      });
    }
  }

  Future<void> _toggleConversation() async {
    final service = ref.read(openAIRealtimeProvider);
    
    if (_isConversationActive) {
      // Stop conversation
      setState(() {
        _isConversationActive = false;
      });
      
      await _audioRecorder.stop();
      await service.stopListening();
      
    } else {
      // Start conversation
      await _checkPermissions();
      
      if (await _audioRecorder.hasPermission()) {
        setState(() {
          _isConversationActive = true;
          // Don't clear conversation history - let it continue
        });
        
        // Start audio stream
        _audioStream = await _audioRecorder.startStream(const RecordConfig(
          encoder: AudioEncoder.pcm16bits,
          sampleRate: 16000,
          numChannels: 1,
        ));

        await service.startListening();

        // Listen to audio stream
        _audioStream!.listen((data) {
          if (_isConversationActive) {
            service.sendAudioData(Uint8List.fromList(data));
          }
        }, onError: (error) {
          debugPrint('Audio stream error: $error');
          setState(() {
            _errorMessage = 'Audio error: $error';
            _isConversationActive = false;
          });
        });
      }
    }
  }

  @override
  void dispose() {
    _audioRecorder.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final service = ref.watch(openAIRealtimeProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0B),
      appBar: AppBar(
        title: const Text('AI Voice Chat'),
        backgroundColor: const Color(0xFF0A0A0B),
        centerTitle: true,
        elevation: 0,
        actions: [
          if (service.isConnected)
            Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: const [
                  Icon(Icons.circle, color: Colors.green, size: 8),
                  SizedBox(width: 4),
                  Text('Connected', style: TextStyle(color: Colors.green, fontSize: 12)),
                ],
              ),
            ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Error Message
            if (_errorMessage != null)
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error_outline, color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red, fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
              
            // Conversation Display
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: GlassMorphismCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        // Header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Conversation',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (_conversationHistory.isNotEmpty)
                              TextButton(
                                onPressed: () {
                                  setState(() {
                                    _conversationHistory.clear();
                                  });
                                },
                                child: Text('Clear', style: TextStyle(color: AppColorPalette.colorAccentOrange)),
                              ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        // Messages
                        Expanded(
                          child: _conversationHistory.isEmpty
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.mic_none,
                                        size: 64,
                                        color: Colors.white.withOpacity(0.2),
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'Press "Start Talking" to begin',
                                        style: TextStyle(
                                          color: Colors.white.withOpacity(0.4),
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : ListView.builder(
                                  controller: _scrollController,
                                  itemCount: _conversationHistory.length,
                                  itemBuilder: (context, index) {
                                    final message = _conversationHistory[index];
                                    final isUser = message['role'] == 'user';
                                    
                                    return Container(
                                      margin: EdgeInsets.only(
                                        bottom: 16,
                                        left: isUser ? 40 : 0,
                                        right: isUser ? 0 : 40,
                                      ),
                                      child: Column(
                                        crossAxisAlignment: isUser 
                                            ? CrossAxisAlignment.end 
                                            : CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            isUser ? 'You' : 'AI Coach',
                                            style: TextStyle(
                                              color: isUser ? AppColorPalette.colorAccentOrange : AppColorPalette.colorNavyBright,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Container(
                                            padding: const EdgeInsets.all(12),
                                            decoration: BoxDecoration(
                                              color: (isUser ? AppColorPalette.colorAccentOrange : AppColorPalette.colorNavyBright)
                                                  .withOpacity(0.1),
                                              borderRadius: BorderRadius.circular(12),
                                              border: Border.all(
                                                color: (isUser ? AppColorPalette.colorAccentOrange : AppColorPalette.colorNavyBright)
                                                    .withOpacity(0.3),
                                              ),
                                            ),
                                            child: Text(
                                              message['message'] ?? '',
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 15,
                                                height: 1.4,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Debug buttons
            if (service.isConnected)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: ElevatedButton(
                        onPressed: () async {
                          await service.testAudioPlayback();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                        ),
                        child: const Text('Test Audio'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Flexible(
                      child: ElevatedButton(
                        onPressed: () async {
                          await service.sendTextMessage("Hello, can you hear me?");
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                        ),
                        child: const Text('Test Text'),
                      ),
                    ),
                  ],
                ),
              ),
            
            const SizedBox(height: 24),
            
            // Main Action Button
            GestureDetector(
              onTap: service.isConnected ? _toggleConversation : null,
              child: Container(
                width: 200,
                height: 60,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  gradient: LinearGradient(
                    colors: !service.isConnected
                        ? [Colors.grey.shade600, Colors.grey.shade800]
                        : _isConversationActive
                            ? [Colors.red.shade400, Colors.red.shade700]
                            : [AppColorPalette.colorAccentOrange, const Color(0xFFFF8E53)],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: (!service.isConnected
                              ? Colors.grey
                              : _isConversationActive
                                  ? Colors.red
                                  : AppColorPalette.colorAccentOrange)
                          .withOpacity(0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _isConversationActive ? Icons.stop : Icons.mic,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        !service.isConnected
                            ? 'Connecting...'
                            : _isConversationActive
                                ? 'Stop Talking'
                                : 'Start Talking',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              _isConversationActive 
                  ? 'Listening... Speak naturally!' 
                  : 'Press to start a conversation',
              style: TextStyle(
                color: Colors.white.withOpacity(0.6),
                fontSize: 14,
              ),
            ),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}