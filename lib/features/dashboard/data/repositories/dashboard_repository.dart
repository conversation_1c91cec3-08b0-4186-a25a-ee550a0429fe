import 'package:flutter/foundation.dart';
import '../../../../shared/services/supabase_service.dart';
import '../../../../shared/repositories/base_repository.dart';
import '../../domain/models/user_stats.dart';
import '../../domain/models/today_workout.dart';

class DashboardRepository extends BaseRepository {
  /// Get user statistics from Supabase with optimized caching
  Future<UserStats> getUserStats() async {
    final userId = getCurrentUserId();
    final cacheKey = 'user_stats_$userId';

    return executeCachedQuery(
      cacheKey,
      () async {
        // Get completed workouts with optimized query
        final completedWorkouts = await executePaginatedQuery<Map<String, dynamic>>(
          'date_completed, duration, calories_burned',
          'completed_workouts',
          {'user_id': userId},
          (json) => json,
          limit: 100, // Reasonable limit for stats calculation
          orderBy: 'date_completed',
          ascending: false,
        );

        // Calculate current streak
        final currentStreak = _calculateStreak(completedWorkouts);

        // Get weekly activity (last 7 days)
        final weekStart = DateTime.now().subtract(const Duration(days: 7));
        final weeklyWorkouts = completedWorkouts
            .where((workout) {
              final date = DateTime.parse(workout['date_completed']);
              return date.isAfter(weekStart);
            })
            .toList();

        // Calculate weekly stats
        final weeklyCalories = weeklyWorkouts.fold<int>(
          0,
          (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0),
        );

        final totalDuration = weeklyWorkouts.fold<int>(
          0,
          (sum, workout) => sum + (workout['duration'] as int? ?? 0),
        );

        final averageDuration = weeklyWorkouts.isNotEmpty
            ? totalDuration / weeklyWorkouts.length
            : 0.0;

        // Generate weekly activity data
        final weeklyActivity = _generateWeeklyActivity(weeklyWorkouts);

        // Get last workout date
        final lastWorkoutDate = completedWorkouts.isNotEmpty
            ? DateTime.parse(completedWorkouts.first['date_completed'])
            : DateTime.now().subtract(const Duration(days: 365));

        // Check if user has workout today
        final today = DateTime.now();
        final hasWorkoutToday = completedWorkouts.any((workout) {
          final date = DateTime.parse(workout['date_completed']);
          return date.year == today.year &&
                 date.month == today.month &&
                 date.day == today.day;
        });

        return UserStats(
          currentStreak: currentStreak,
          weeklyWorkouts: weeklyWorkouts.length,
          weeklyCalories: weeklyCalories,
          totalWorkouts: completedWorkouts.length,
          averageWorkoutDuration: averageDuration,
          weeklyActivity: weeklyActivity,
          lastWorkoutDate: lastWorkoutDate,
          hasWorkoutToday: hasWorkoutToday,
          workoutsThisWeek: weeklyWorkouts.length,
          totalCalories: weeklyCalories,
        );
      },
      cacheExpiry: BaseRepository.longCache, // Cache for 15 minutes
      operation: 'get user statistics',
    );
  }

  /// Get today's assigned workout
  Future<TodayWorkout?> getTodayWorkout() async {
    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('🔍 Fetching today\'s workout for user: $userId');

      // Get user's most recent active workout with explicit filtering
      // Only get workouts that have exercises
      final workoutResponse = await SupabaseService.client
          .from('workouts')
          .select('''
            id,
            name,
            ai_description,
            is_completed,
            is_active,
            start_time,
            end_time,
            created_at,
            workout_exercises (
              id,
              name,
              sets,
              reps,
              weight,
              reps_old,
              weight_old,
              rest_interval,
              completed,
              exercises (
                id,
                name,
                description,
                video_url,
                vertical_video,
                instructions
              )
            )
          ''')
          .eq('user_id', userId)
          .eq('is_completed', false)
          .not('workout_exercises', 'is', null) // Ensure workout has exercises
          .order('created_at', ascending: false);

      // Filter for workouts that actually have exercises
      List<Map<String, dynamic>> workoutsWithExercises = [];
      for (final workout in workoutResponse) {
        final exercises = workout['workout_exercises'] as List? ?? [];
        if (exercises.isNotEmpty) {
          workoutsWithExercises.add(workout);
        }
      }

      if (workoutsWithExercises.isEmpty) {
        debugPrint('✅ No incomplete workouts with exercises found - all workouts completed or no exercises');
        return null;
      }

      // Get the most recent workout with exercises
      final workoutData = workoutsWithExercises.first;

      // Additional validation to ensure workout is truly not completed
      final isCompleted = workoutData['is_completed'] as bool? ?? false;
      
      if (isCompleted) {
        debugPrint('⚠️ Workout filtered out: completed=$isCompleted');
        return null;
      }

      debugPrint('✅ Found incomplete workout: ${workoutData['name']}');

      // Convert to TodayWorkout model
      final workoutExercises = workoutData['workout_exercises'] as List? ?? [];
      if (workoutExercises.isEmpty) {
        debugPrint('⚠️ Workout has no exercises after filtering - this shouldn\'t happen');
        return null;
      }

      final exercises = workoutExercises
          .map((exerciseData) => _mapToWorkoutExercise(exerciseData))
          .toList();

      final totalSets = exercises.fold<int>(
        0,
        (sum, exercise) => sum + exercise.sets,
      );

      // Estimate duration (2 minutes per set + rest intervals)
      final estimatedDuration = exercises.fold<int>(
        0,
        (sum, exercise) => sum + (exercise.sets * 2) +
                          ((exercise.sets - 1) * (exercise.restInterval ~/ 60)),
      );

      // Estimate calories (rough calculation based on duration)
      final estimatedCalories = (estimatedDuration * 8).round();

      final todayWorkout = TodayWorkout(
        id: workoutData['id'],
        name: workoutData['name'],
        description: workoutData['ai_description'] ?? 'Your personalized workout',
        estimatedDuration: estimatedDuration,
        estimatedCalories: estimatedCalories,
        totalSets: totalSets,
        exercises: exercises,
        isCompleted: isCompleted,
        isStarted: workoutData['start_time'] != null,
        startedAt: workoutData['start_time'] != null
            ? DateTime.parse(workoutData['start_time'])
            : null,
        completedAt: workoutData['end_time'] != null
            ? DateTime.parse(workoutData['end_time'])
            : null,
      );

      debugPrint('📋 Today\'s workout prepared: ${todayWorkout.name} (${todayWorkout.exercises.length} exercises)');
      return todayWorkout;

    } catch (e) {
      debugPrint('❌ Error fetching today\'s workout: $e');
      throw Exception('Failed to fetch today\'s workout: ${e.toString()}');
    }
  }

  /// Calculate current workout streak
  int _calculateStreak(List<dynamic> completedWorkouts) {
    if (completedWorkouts.isEmpty) return 0;

    int streak = 0;
    DateTime currentDate = DateTime.now();
    
    // Check if user worked out today
    final today = completedWorkouts.any((workout) {
      final date = DateTime.parse(workout['date_completed']);
      return date.year == currentDate.year &&
             date.month == currentDate.month &&
             date.day == currentDate.day;
    });

    if (!today) {
      // If no workout today, start checking from yesterday
      currentDate = currentDate.subtract(const Duration(days: 1));
    }

    // Count consecutive days with workouts
    for (int i = 0; i < completedWorkouts.length; i++) {
      final workoutDate = DateTime.parse(completedWorkouts[i]['date_completed']);
      
      if (workoutDate.year == currentDate.year &&
          workoutDate.month == currentDate.month &&
          workoutDate.day == currentDate.day) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else if (workoutDate.isBefore(currentDate)) {
        break;
      }
    }

    return streak;
  }

  /// Generate weekly activity data
  List<WeeklyActivity> _generateWeeklyActivity(List<dynamic> weeklyWorkouts) {
    final List<WeeklyActivity> activity = [];
    final now = DateTime.now();

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayWorkouts = weeklyWorkouts.where((workout) {
        final workoutDate = DateTime.parse(workout['date_completed']);
        return workoutDate.year == date.year &&
               workoutDate.month == date.month &&
               workoutDate.day == date.day;
      }).toList();

      final totalMinutes = dayWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['duration'] as int? ?? 0),
      );

      final totalCalories = dayWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0),
      );

      activity.add(WeeklyActivity(
        day: _getDayAbbreviation(date.weekday),
        minutes: totalMinutes,
        calories: totalCalories,
        date: date,
      ));
    }

    return activity;
  }

  /// Convert workout exercise data to TodayWorkoutExercise model
  TodayWorkoutExercise _mapToWorkoutExercise(Map<String, dynamic> data) {
    final exerciseData = data['exercises'];

    // Handle reps array - try new format first, fallback to old format
    List<int> reps = [10]; // default
    if (data['reps'] != null) {
      try {
        final repsData = data['reps'] as List<dynamic>;
        reps = repsData.map((r) => r is int ? r : int.tryParse(r.toString()) ?? 10).toList();
      } catch (e) {
        debugPrint('Error parsing reps array: $e');
      }
    } else if (data['reps_old'] != null) {
      // Fallback to old single rep format
      final oldReps = data['reps_old'] as int? ?? 10;
      final sets = data['sets'] as int? ?? 3;
      reps = List.filled(sets, oldReps);
    }

    // Handle weights array - try new format first, fallback to old format
    List<double> weights = [0.0]; // default
    if (data['weight'] != null) {
      try {
        final weightsData = data['weight'] as List<dynamic>;
        weights = weightsData.map((w) => w is double ? w : double.tryParse(w.toString()) ?? 0.0).toList();
      } catch (e) {
        debugPrint('Error parsing weights array: $e');
      }
    } else if (data['weight_old'] != null) {
      // Fallback to old single weight format
      final oldWeight = (data['weight_old'] as num?)?.toDouble() ?? 0.0;
      final sets = data['sets'] as int? ?? 3;
      weights = List.filled(sets, oldWeight);
    }

    return TodayWorkoutExercise(
      id: data['id'],
      name: data['name'] ?? exerciseData?['name'] ?? 'Unknown Exercise',
      description: exerciseData?['description'] ?? '',
      sets: data['sets'] ?? 3,
      reps: reps,
      weights: weights,
      videoUrl: exerciseData?['video_url'],
      thumbnailUrl: exerciseData?['vertical_video'],
      instructions: exerciseData?['instructions'],
      restInterval: (data['rest_interval'] as int?) ?? 60,
      isCompleted: data['completed'] ?? false,
    );
  }

  /// Get day abbreviation for weekday
  String _getDayAbbreviation(int weekday) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[weekday - 1];
  }

  /// Get the current active workout (oldest uncompleted workout by creation time)
  Future<TodayWorkout?> getCurrentWorkout() async {
    try {
      final allWorkouts = await getAllWorkouts();

      // Find uncompleted workouts
      final uncompletedWorkouts = allWorkouts.where((workout) => !workout.isCompleted).toList();

      if (uncompletedWorkouts.isEmpty) {
        return null; // No active workouts
      }

      // Return the first uncompleted workout
      return uncompletedWorkouts.first;
    } catch (e) {
      debugPrint('Error fetching current workout: $e');
      return null;
    }
  }

  /// Get all available workouts for the workout list screen
  Future<List<TodayWorkout>> getAllWorkouts() async {
    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get user's workouts with exercises
      final workoutResponse = await SupabaseService.client
          .from('workouts')
          .select('''
            id,
            name,
            ai_description,
            is_completed,
            start_time,
            end_time,
            workout_exercises (
              id,
              name,
              sets,
              reps,
              weight,
              reps_old,
              weight_old,
              rest_interval,
              completed,
              exercises (
                id,
                name,
                description,
                video_url,
                vertical_video,
                instructions,
                primary_muscle,
                equipment
              )
            )
          ''')
          .eq('user_id', userId)
          .eq('is_completed', false)
          .not('workout_exercises', 'is', null) // Ensure workout has exercises
          .order('created_at', ascending: false)
          .limit(20); // Increased limit to account for filtering

      if (workoutResponse.isEmpty) {
        debugPrint('No workouts found for user');
        return [];
      }

      final workouts = <TodayWorkout>[];
      for (final workoutData in workoutResponse) {
        // Only include workouts that have exercises
        final workoutExercises = workoutData['workout_exercises'] as List? ?? [];
        if (workoutExercises.isNotEmpty) {
          final workout = _mapToTodayWorkout(workoutData);
          if (workout != null) {
            workouts.add(workout);
          }
        }
      }

      debugPrint('✅ Found ${workouts.length} workouts with exercises');
      return workouts.take(10).toList(); // Return top 10 workouts with exercises
    } catch (e) {
      debugPrint('Error fetching all workouts: $e');
      throw Exception('Failed to fetch workouts: ${e.toString()}');
    }
  }

  /// Get completed workouts for workout history
  Future<List<Map<String, dynamic>>> getCompletedWorkouts({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final completedWorkoutResponse = await SupabaseService.client
          .from('completed_workouts')
          .select('''
            id,
            workout_id,
            date_completed,
            duration,
            calories_burned,
            rating,
            user_feedback_completed_workout,
            completed_workout_summary
          ''')
          .eq('user_id', userId)
          .order('date_completed', ascending: false)
          .range(offset, offset + limit - 1);

      debugPrint('Found ${completedWorkoutResponse.length} completed workouts');
      return List<Map<String, dynamic>>.from(completedWorkoutResponse);

    } catch (e) {
      debugPrint('Error fetching completed workouts: $e');
      throw Exception('Failed to fetch completed workouts: ${e.toString()}');
    }
  }

  /// Get workout statistics from completed workouts
  Future<Map<String, dynamic>> getWorkoutStatistics() async {
    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final completedWorkouts = await getCompletedWorkouts(limit: 1000);
      
      if (completedWorkouts.isEmpty) {
        return {
          'total_workouts': 0,
          'total_duration_minutes': 0,
          'total_calories_burned': 0,
          'average_rating': 0.0,
          'current_streak': 0,
        };
      }

      final totalWorkouts = completedWorkouts.length;
      final totalDuration = completedWorkouts.fold<int>(
        0, 
        (sum, workout) => sum + (workout['duration'] as int? ?? 0),
      );
      final totalCalories = completedWorkouts.fold<int>(
        0, 
        (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0),
      );
      
      // Calculate average rating (only from rated workouts)
      final ratedWorkouts = completedWorkouts.where((w) => w['rating'] != null);
      final averageRating = ratedWorkouts.isNotEmpty
          ? ratedWorkouts.fold<double>(0, (sum, w) => sum + (w['rating'] as num).toDouble()) / ratedWorkouts.length
          : 0.0;

      // Calculate current streak
      final currentStreak = _calculateCurrentStreak(completedWorkouts);

      return {
        'total_workouts': totalWorkouts,
        'total_duration_minutes': (totalDuration / 60).round(),
        'total_calories_burned': totalCalories,
        'average_rating': averageRating,
        'current_streak': currentStreak,
      };

    } catch (e) {
      debugPrint('Error calculating workout statistics: $e');
      throw Exception('Failed to calculate workout statistics: ${e.toString()}');
    }
  }

  /// Calculate current workout streak
  int _calculateCurrentStreak(List<Map<String, dynamic>> completedWorkouts) {
    if (completedWorkouts.isEmpty) return 0;

    int streak = 0;
    DateTime? lastWorkoutDate;

    for (final workout in completedWorkouts) {
      final dateCompleted = DateTime.parse(workout['date_completed']);
      final workoutDate = DateTime(dateCompleted.year, dateCompleted.month, dateCompleted.day);

      if (lastWorkoutDate == null) {
        // First workout
        final today = DateTime.now();
        final todayDate = DateTime(today.year, today.month, today.day);
        
        // Check if workout was today or yesterday
        final difference = todayDate.difference(workoutDate).inDays;
        if (difference <= 1) {
          streak = 1;
          lastWorkoutDate = workoutDate;
        } else {
          break; // Streak is broken
        }
      } else {
        // Check if this workout was the day before the last workout
        final difference = lastWorkoutDate.difference(workoutDate).inDays;
        if (difference == 1) {
          streak++;
          lastWorkoutDate = workoutDate;
        } else if (difference == 0) {
          // Same day, continue
          continue;
        } else {
          break; // Streak is broken
        }
      }
    }

    return streak;
  }

  /// Map database workout data to TodayWorkout model
  TodayWorkout? _mapToTodayWorkout(Map<String, dynamic> workoutData) {
    try {
      final workoutExercises = workoutData['workout_exercises'] as List<dynamic>? ?? [];

      if (workoutExercises.isEmpty) {
        return null; // Skip workouts without exercises
      }

      final exercises = <TodayWorkoutExercise>[];
      int totalSets = 0;

      for (final exerciseData in workoutExercises) {
        final exercise = _mapToWorkoutExercise(exerciseData);
        exercises.add(exercise);
        totalSets += exercise.sets;
      }

      // Estimate duration based on exercises (rough calculation)
      final estimatedDuration = (exercises.length * 8 + totalSets * 2).clamp(15, 90);

      // Estimate calories (rough calculation based on duration)
      final estimatedCalories = (estimatedDuration * 8).round();

      return TodayWorkout(
        id: workoutData['id'],
        name: workoutData['name'] ?? 'Unnamed Workout',
        description: workoutData['ai_description'] ?? 'Your personalized workout',
        estimatedDuration: estimatedDuration,
        estimatedCalories: estimatedCalories,
        totalSets: totalSets,
        exercises: exercises,
        isCompleted: workoutData['is_completed'] ?? false,
        isStarted: workoutData['start_time'] != null,
        startedAt: workoutData['start_time'] != null
            ? DateTime.parse(workoutData['start_time'])
            : null,
        completedAt: workoutData['end_time'] != null
            ? DateTime.parse(workoutData['end_time'])
            : null,
      );
    } catch (e) {
      debugPrint('Error mapping workout data: $e');
      return null;
    }
  }
}
