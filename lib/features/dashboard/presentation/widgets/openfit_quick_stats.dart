import 'package:flutter/material.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../domain/models/user_stats.dart';

/// OpenFit quick stats section with glass morphism mini cards
class OpenFitQuickStats extends StatefulWidget {
  final UserStats? userStats;

  const OpenFitQuickStats({
    super.key,
    this.userStats,
  });

  @override
  State<OpenFitQuickStats> createState() => _OpenFitQuickStatsState();
}

class _OpenFitQuickStatsState extends State<OpenFitQuickStats>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _fadeAnimations;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: Duration(milliseconds: 600 + (index * 100)),
        vsync: this,
      );
    });

    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.elasticOut,
      ));
    }).toList();

    _fadeAnimations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      ));
    }).toList();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.userStats == null) {
      return _buildLoadingStats();
    }

    final stats = widget.userStats!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(
            left: AppSpacing.sm,
            bottom: AppSpacing.md,
          ),
          child: Text(
            'Quick Stats',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        LayoutBuilder(
          builder: (context, constraints) {
            return Row(
              children: [
                Expanded(
                  child: _buildAnimatedStatCard(
                    index: 0,
                    icon: Icons.local_fire_department,
                    value: '${stats.currentStreak}',
                    label: 'Day\nStreak',
                    color: AppColorPalette.accentPrimary,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _buildAnimatedStatCard(
                    index: 1,
                    icon: Icons.calendar_today,
                    value: '${stats.workoutsThisWeek}',
                    label: 'This\nWeek',
                    color: AppColorPalette.accentBlue,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _buildAnimatedStatCard(
                    index: 2,
                    icon: Icons.trending_up,
                    value: '${stats.totalCalories}',
                    label: 'Total\nCals',
                    color: AppColorPalette.successGreen,
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildAnimatedStatCard({
    required int index,
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimations[index],
        _fadeAnimations[index],
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimations[index].value,
          child: Opacity(
            opacity: _fadeAnimations[index].value,
            child: _buildStatCard(
              icon: icon,
              value: value,
              label: label,
              color: color,
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return GlassMorphismCard(
      height: 110,
      padding: const EdgeInsets.all(12),
      borderColor: color.withOpacity(0.3),
      gradientColors: [
        color.withOpacity(0.1),
        color.withOpacity(0.05),
      ],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),

          const SizedBox(height: 6),

          // Value
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),

          const SizedBox(height: 2),

          // Label
          Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 10,
              fontWeight: FontWeight.w500,
              height: 1.1,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            left: AppSpacing.sm,
            bottom: AppSpacing.md,
          ),
          child: Container(
            width: 100,
            height: 18,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(9),
            ),
          ),
        ),
        Row(
          children: List.generate(3, (index) {
            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  right: index < 2 ? AppSpacing.md : 0,
                ),
                child: const GlassMorphismCard(
                  height: 110,
                  child: Center(
                    child: CircularProgressIndicator(
                      color: AppColorPalette.primaryOrange,
                      strokeWidth: 2,
                    ),
                  ),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }
}

/// Individual stat card with pulse animation for achievements
class PulsingStatCard extends StatefulWidget {
  final IconData icon;
  final String value;
  final String label;
  final Color color;
  final bool shouldPulse;

  const PulsingStatCard({
    super.key,
    required this.icon,
    required this.value,
    required this.label,
    required this.color,
    this.shouldPulse = false,
  });

  @override
  State<PulsingStatCard> createState() => _PulsingStatCardState();
}

class _PulsingStatCardState extends State<PulsingStatCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    if (widget.shouldPulse) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(PulsingStatCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.shouldPulse && !oldWidget.shouldPulse) {
      _pulseController.repeat(reverse: true);
    } else if (!widget.shouldPulse && oldWidget.shouldPulse) {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: GlassMorphismCard(
            height: 110,
            padding: const EdgeInsets.all(12),
            borderColor: widget.color.withOpacity(0.3),
            gradientColors: [
              widget.color.withOpacity(0.1),
              widget.color.withOpacity(0.05),
            ],
            boxShadow: widget.shouldPulse
                ? [
                    BoxShadow(
                      color: widget.color.withOpacity(0.4),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ]
                : null,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: widget.color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    widget.icon,
                    color: widget.color,
                    size: 16,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  widget.value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                const SizedBox(height: 2),
                Text(
                  widget.label,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    height: 1.1,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
