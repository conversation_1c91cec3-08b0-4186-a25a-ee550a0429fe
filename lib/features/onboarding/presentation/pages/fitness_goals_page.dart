import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/providers/onboarding_provider.dart';
import '../../domain/models/onboarding_state.dart';
import '../../../profile/domain/models/user_profile.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';

class FitnessGoalsPage extends ConsumerStatefulWidget {
  const FitnessGoalsPage({super.key});

  @override
  ConsumerState<FitnessGoalsPage> createState() => _FitnessGoalsPageState();
}

class _FitnessGoalsPageState extends ConsumerState<FitnessGoalsPage> {
  late TextEditingController _sportController;
  late TextEditingController _healthInfoController;

  @override
  void initState() {
    super.initState();
    _sportController = TextEditingController();
    _healthInfoController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final state = ref.read(onboardingProvider);
      _sportController.text = state.sportActivity ?? '';
      _healthInfoController.text = state.additionalHealthInfo ?? '';
    });
  }

  @override
  void dispose() {
    _sportController.dispose();
    _healthInfoController.dispose();
    super.dispose();
  }

  void _toggleGoal(FitnessGoal goal) {
    ref.read(onboardingProvider.notifier).toggleFitnessGoal(goal.displayName);
  }

  void _updateSportActivity(String value) {
    ref.read(onboardingProvider.notifier).updateFitnessGoals(sportActivity: value);
  }

  void _updateHealthInfo(String value) {
    ref.read(onboardingProvider.notifier).updateFitnessGoals(additionalHealthInfo: value);
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(onboardingProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Fitness Goals Grid
          ...FitnessGoal.values.map((goal) {
            final isSelected = state.selectedGoals.contains(goal.displayName);
            final isRecommended = goal == FitnessGoal.optimizeHealth || 
                                 goal == FitnessGoal.weightLoss ||
                                 goal == FitnessGoal.increaseStamina;
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _FitnessGoalCard(
                goal: goal,
                isSelected: isSelected,
                isRecommended: isRecommended,
                onTap: () => _toggleGoal(goal),
                onSportActivityChanged: goal == FitnessGoal.sportTraining 
                    ? _updateSportActivity 
                    : null,
                sportController: goal == FitnessGoal.sportTraining 
                    ? _sportController 
                    : null,
                showSportInput: isSelected && goal == FitnessGoal.sportTraining,
              ),
            );
          }),

          const SizedBox(height: 24),

          // Additional Health Info
          GlassMorphismCard(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Is there anything else I should know as your personal health coach?',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _healthInfoController,
                    onChanged: _updateHealthInfo,
                    maxLines: 3,
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'Any health conditions, injuries, or specific considerations...',
                      hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
                      filled: true,
                      fillColor: Colors.white.withOpacity(0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }
}

class _FitnessGoalCard extends StatelessWidget {
  final FitnessGoal goal;
  final bool isSelected;
  final bool isRecommended;
  final VoidCallback onTap;
  final Function(String)? onSportActivityChanged;
  final TextEditingController? sportController;
  final bool showSportInput;

  const _FitnessGoalCard({
    required this.goal,
    required this.isSelected,
    required this.isRecommended,
    required this.onTap,
    this.onSportActivityChanged,
    this.sportController,
    this.showSportInput = false,
  });

  String get _getDescription {
    switch (goal) {
      case FitnessGoal.optimizeHealth:
        return 'Improve overall health, energy, and well-being through balanced fitness';
      case FitnessGoal.sportTraining:
        return 'Train specifically for your sport or activity with targeted exercises';
      case FitnessGoal.buildMuscle:
        return 'Focus on muscle growth and size through strength training';
      case FitnessGoal.weightLoss:
        return 'Lose weight through cardio and strength training combination';
      case FitnessGoal.increaseStamina:
        return 'Build endurance and cardiovascular fitness';
      case FitnessGoal.increaseStrength:
        return 'Develop maximum strength and power through heavy lifting';
    }
  }

  String? get _getRecommendedText {
    switch (goal) {
      case FitnessGoal.optimizeHealth:
        return 'Recommended for Everyday Users';
      case FitnessGoal.weightLoss:
        return 'Recommended for those new to fitness';
      case FitnessGoal.increaseStamina:
        return 'Recommended for Endurance Focus';
      default:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          border: Border.all(
            color: isSelected
                ? const Color(0xFFFF6B35)
                : Colors.white.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          goal.displayName,
                          style: TextStyle(
                            color: isSelected ? const Color(0xFFFF6B35) : Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      if (isSelected)
                        Container(
                          width: 24,
                          height: 24,
                          decoration: const BoxDecoration(
                            color: Color(0xFFFF6B35),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                    ],
                  ),
                  
                  if (isRecommended) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF6B35).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        _getRecommendedText!,
                        style: const TextStyle(
                          color: Color(0xFFFF6B35),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                  
                  const SizedBox(height: 12),
                  Text(
                    _getDescription,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
            
            // Sport Activity Input (only for sport training goal)
            if (showSportInput && sportController != null) ...[
              Container(
                width: double.infinity,
                height: 1,
                color: Colors.white.withOpacity(0.1),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "What's your sport or activity?",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: sportController,
                      onChanged: onSportActivityChanged,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        hintText: 'e.g., Basketball, Running, Swimming...',
                        hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
                        filled: true,
                        fillColor: Colors.white.withOpacity(0.1),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
