import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../dashboard/domain/models/today_workout.dart';
import '../../domain/providers/workout_session_provider.dart';

/// Refined workout details screen matching the detailed specification
class RefinedWorkoutDetailsScreen extends ConsumerStatefulWidget {
  final TodayWorkout workout;

  const RefinedWorkoutDetailsScreen({
    super.key,
    required this.workout,
  });

  @override
  ConsumerState<RefinedWorkoutDetailsScreen> createState() => _RefinedWorkoutDetailsScreenState();
}

class _RefinedWorkoutDetailsScreenState extends ConsumerState<RefinedWorkoutDetailsScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: Stack(
        children: [
          // Main scrollable content
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Hero image section
              _buildHeroSection(size),
              
              // Content section
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Column(
                      children: [
                        // Workout title and description
                        _buildWorkoutHeader(),
                        
                        // Stats row
                        _buildStatsRow(),
                        
                        // Exercise list
                        _buildExerciseList(),
                        
                        // Bottom padding for start button
                        const SizedBox(height: 100),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          // Back button
          _buildBackButton(context),
          
          // Start workout button (pinned at bottom)
          _buildStartWorkoutButton(context),
        ],
      ),
    );
  }

  Widget _buildHeroSection(Size size) {
    return SliverAppBar(
      expandedHeight: size.height * 0.4,
      pinned: false,
      automaticallyImplyLeading: false,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // Background image
            if (widget.workout.backgroundImageUrl != null)
              Image.network(
                widget.workout.backgroundImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultBackground(),
              )
            else
              _buildDefaultBackground(),
            
            // Dark gradient overlay for legibility
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.3),
                    Colors.black.withOpacity(0.7),
                  ],
                  stops: const [0.0, 0.6, 1.0],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultBackground() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColorPalette.primaryOrange,
            AppColorPalette.primaryOrangeLight,
            AppColorPalette.darkBackground,
          ],
        ),
      ),
    );
  }

  Widget _buildBackButton(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      left: 20,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            if (context.canPop()) {
              context.pop();
            }
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWorkoutHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workout title - prominent, centered
          Center(
            child: Text(
              widget.workout.name,
              style: AppTypography.displayNumbers(
                fontSize: 28,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Workout description - one or two lines below title
          Center(
            child: Text(
              widget.workout.description,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 16,
                fontWeight: FontWeight.w400,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          // Time stat
          Expanded(
            child: _buildStatColumn(
              icon: Icons.timer_outlined,
              value: '${widget.workout.estimatedDuration}min',
              label: 'Time',
            ),
          ),
          
          // Calories stat
          Expanded(
            child: _buildStatColumn(
              icon: Icons.local_fire_department_outlined,
              value: '${widget.workout.estimatedCalories}kcal',
              label: 'Calories',
            ),
          ),
          
          // Exercises count stat
          Expanded(
            child: _buildStatColumn(
              icon: Icons.fitness_center_outlined,
              value: '${widget.workout.exercises.length}x${widget.workout.totalSets ~/ widget.workout.exercises.length}',
              label: 'Sets',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatColumn({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Column(
      children: [
        // Icon above value
        Icon(
          icon,
          color: AppColorPalette.primaryOrange,
          size: 24,
        ),
        
        const SizedBox(height: 8),
        
        // Value
        Text(
          value,
          style: AppTypography.displayNumbers(
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: 4),
        
        // Label
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildExerciseList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            'Exercises',
            style: AppTypography.displayNumbers(
              fontSize: 22,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 16),

          // Exercise cards
          ...widget.workout.exercises.asMap().entries.map((entry) {
            final index = entry.key;
            final exercise = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildExerciseCard(exercise, index + 1),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildExerciseCard(TodayWorkoutExercise exercise, int exerciseNumber) {
    return GlassMorphismCard(
      child: InkWell(
        onTap: () => _showExerciseDetails(exercise),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Thumbnail image
              _buildExerciseThumbnail(exercise),

              const SizedBox(width: 16),

              // Exercise info
              Expanded(
                child: _buildExerciseInfo(exercise, exerciseNumber),
              ),

              // Three-dot menu
              _buildThreeDotMenu(exercise),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExerciseThumbnail(TodayWorkoutExercise exercise) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColorPalette.primaryOrange.withOpacity(0.1),
        border: Border.all(
          color: AppColorPalette.primaryOrange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: exercise.thumbnailUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                exercise.thumbnailUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultThumbnail(),
              ),
            )
          : _buildDefaultThumbnail(),
    );
  }

  Widget _buildDefaultThumbnail() {
    return const Icon(
      Icons.fitness_center,
      color: AppColorPalette.primaryOrange,
      size: 28,
    );
  }

  Widget _buildExerciseInfo(TodayWorkoutExercise exercise, int exerciseNumber) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Exercise name
        Text(
          exercise.name,
          style: AppTypography.displayNumbers(
            fontSize: 16,
            color: Colors.white,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),

        const SizedBox(height: 6),

        // Exercise details (sets × reps format)
        Text(
          _formatExerciseDetails(exercise),
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _formatExerciseDetails(TodayWorkoutExercise exercise) {
    if (exercise.reps.isNotEmpty) {
      final reps = exercise.reps.first;
      return '${exercise.sets} × $reps reps';
    }
    return '${exercise.sets} sets';
  }

  Widget _buildThreeDotMenu(TodayWorkoutExercise exercise) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: Colors.white.withOpacity(0.7),
        size: 20,
      ),
      color: AppColorPalette.darkBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      onSelected: (value) => _handleMenuAction(value, exercise),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit_outlined, color: Colors.white, size: 18),
              SizedBox(width: 12),
              Text('Edit Exercise', style: TextStyle(color: Colors.white)),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'details',
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.white, size: 18),
              SizedBox(width: 12),
              Text('View Details', style: TextStyle(color: Colors.white)),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'remove',
          child: Row(
            children: [
              Icon(Icons.delete_outline, color: Colors.red, size: 18),
              SizedBox(width: 12),
              Text('Remove', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStartWorkoutButton(BuildContext context) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 16,
          bottom: MediaQuery.of(context).padding.bottom + 16,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColorPalette.darkBackground.withOpacity(0.0),
              AppColorPalette.darkBackground.withOpacity(0.8),
              AppColorPalette.darkBackground,
            ],
          ),
        ),
        child: GestureDetector(
          onTap: () => _startWorkout(context),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [
                  AppColorPalette.primaryOrange,
                  AppColorPalette.primaryOrangeLight,
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColorPalette.primaryOrange.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'Start Workout',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showExerciseDetails(TodayWorkoutExercise exercise) {
    HapticFeedback.lightImpact();
    // TODO: Show exercise details modal or navigate to exercise details screen
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildExerciseDetailsModal(exercise),
    );
  }

  Widget _buildExerciseDetailsModal(TodayWorkoutExercise exercise) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: AppColorPalette.darkBackground,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exercise.name,
                    style: AppTypography.displayNumbers(
                      fontSize: 24,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    exercise.description,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _formatExerciseDetails(exercise),
                    style: const TextStyle(
                      color: AppColorPalette.primaryOrange,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  // TODO: Add more exercise details, instructions, video player, etc.
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action, TodayWorkoutExercise exercise) {
    HapticFeedback.lightImpact();
    switch (action) {
      case 'edit':
        // TODO: Navigate to exercise edit screen
        break;
      case 'details':
        _showExerciseDetails(exercise);
        break;
      case 'remove':
        // TODO: Show confirmation dialog and remove exercise
        break;
    }
  }

  void _startWorkout(BuildContext context) {
    HapticFeedback.mediumImpact();
    // Navigate to workout loading screen with the selected workout
    context.go('/workout-loading', extra: widget.workout.id);
  }
}
