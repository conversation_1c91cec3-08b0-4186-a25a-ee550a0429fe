import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../domain/providers/custom_workout_provider.dart';

class SimpleCustomWorkoutScreen extends ConsumerStatefulWidget {
  const SimpleCustomWorkoutScreen({super.key});

  @override
  ConsumerState<SimpleCustomWorkoutScreen> createState() => _SimpleCustomWorkoutScreenState();
}

class _SimpleCustomWorkoutScreenState extends ConsumerState<SimpleCustomWorkoutScreen> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customWorkoutState = ref.watch(customWorkoutProvider);
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Create Custom Workout'),
        backgroundColor: theme.appBarTheme.backgroundColor,
        foregroundColor: theme.appBarTheme.foregroundColor,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Workout Name Input
                    Text(
                      'Workout Name',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        hintText: 'Enter workout name',
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a workout name';
                        }
                        if (value.trim().length < 3) {
                          return 'Workout name must be at least 3 characters';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Workout Description Input
                    Text(
                      'Description (Optional)',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _descriptionController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        hintText: 'Describe your workout...',
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Exercises Section
                    Row(
                      children: [
                        Text(
                          'Exercises',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColorPalette.primaryOrange.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${customWorkoutState.exercises.length}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppColorPalette.primaryOrange,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    if (customWorkoutState.exercises.isEmpty)
                      _buildEmptyExercisesState(theme)
                    else
                      _buildExercisesList(customWorkoutState.exercises, theme),
                    
                    const SizedBox(height: 24),
                    
                    // Add Exercise Button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _addExercise,
                        icon: const Icon(Icons.add),
                        label: const Text('Add Exercise'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side: const BorderSide(color: AppColorPalette.primaryOrange),
                          foregroundColor: AppColorPalette.primaryOrange,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 100), // Space for bottom buttons
                  ],
                ),
              ),
            ),
            
            // Bottom Action Buttons
            _buildBottomActions(customWorkoutState, theme),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyExercisesState(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.fitness_center_outlined,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No exercises added yet',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap "Add Exercise" to start building your workout',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildExercisesList(List<CustomWorkoutExercise> exercises, ThemeData theme) {
    return Column(
      children: exercises.asMap().entries.map((entry) {
        final index = entry.key;
        final exercise = entry.value;
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildExerciseCard(exercise, index, theme),
        );
      }).toList(),
    );
  }

  Widget _buildExerciseCard(CustomWorkoutExercise exercise, int index, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exercise Header
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppColorPalette.primaryOrange.withOpacity(0.1),
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      color: AppColorPalette.primaryOrange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        exercise.name,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (exercise.description.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          exercise.description,
                          style: theme.textTheme.bodySmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'delete') {
                      _deleteExercise(index);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20),
                          SizedBox(width: 12),
                          Text('Delete'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Exercise Configuration
            Wrap(
              spacing: 12,
              children: [
                _buildConfigChip('${exercise.sets} sets', Icons.repeat, theme),
                _buildConfigChip('${exercise.reps.first} reps', Icons.numbers, theme),
                _buildConfigChip('${exercise.weights.first.toInt()}lbs', Icons.fitness_center, theme),
                if (exercise.restInterval > 0)
                  _buildConfigChip('${exercise.restInterval}s rest', Icons.timer, theme),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigChip(String label, IconData icon, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: theme.colorScheme.onSurfaceVariant),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(CustomWorkoutState state, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: theme.colorScheme.outline.withOpacity(0.1)),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _previewWorkout,
                child: const Text('Preview'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: state.isLoading || state.exercises.isEmpty 
                    ? null 
                    : _saveWorkout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColorPalette.primaryOrange,
                  foregroundColor: Colors.white,
                ),
                child: state.isLoading 
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('Save Workout'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addExercise() {
    // For now, add a simple demo exercise
    // TODO: Navigate to exercise selection screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Exercise'),
        content: const Text('Exercise selection coming soon!\n\nFor now, we\'ll add a demo exercise.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Add demo exercise
              ref.read(customWorkoutProvider.notifier).addExercise(
                exerciseId: 'demo-${DateTime.now().millisecondsSinceEpoch}',
                name: 'Push-ups',
                description: 'Basic bodyweight push-up exercise',
                sets: 3,
                reps: [10],
                weights: [0.0],
                restInterval: 60,
              );
              Navigator.of(context).pop();
            },
            child: const Text('Add Demo Exercise'),
          ),
        ],
      ),
    );
  }

  void _deleteExercise(int index) {
    ref.read(customWorkoutProvider.notifier).removeExercise(index);
  }

  void _previewWorkout() {
    if (!_formKey.currentState!.validate()) return;
    
    final customWorkoutState = ref.read(customWorkoutProvider);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Workout Preview'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Name: ${_nameController.text}'),
            if (_descriptionController.text.isNotEmpty)
              Text('Description: ${_descriptionController.text}'),
            const SizedBox(height: 16),
            Text('Exercises: ${customWorkoutState.exercises.length}'),
            ...customWorkoutState.exercises.map((ex) => 
              Text('• ${ex.name} - ${ex.sets} sets')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _saveWorkout() async {
    if (!_formKey.currentState!.validate()) return;
    
    final customWorkoutState = ref.read(customWorkoutProvider);
    if (customWorkoutState.exercises.isEmpty) {
      _showErrorSnackBar('Please add at least one exercise');
      return;
    }

    try {
      await ref.read(customWorkoutProvider.notifier).saveWorkout(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
      );

      if (mounted) {
        _showSuccessSnackBar('Workout saved successfully!');
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to save workout: ${e.toString()}');
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColorPalette.successGreen,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColorPalette.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
} 