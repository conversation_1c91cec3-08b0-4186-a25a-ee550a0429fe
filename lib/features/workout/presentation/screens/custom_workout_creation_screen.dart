import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../../dashboard/domain/models/today_workout.dart';
import '../../domain/providers/custom_workout_provider.dart';
import 'exercise_selection_screen.dart';

class CustomWorkoutCreationScreen extends ConsumerStatefulWidget {
  const CustomWorkoutCreationScreen({super.key});

  @override
  ConsumerState<CustomWorkoutCreationScreen> createState() => _CustomWorkoutCreationScreenState();
}

class _CustomWorkoutCreationScreenState extends ConsumerState<CustomWorkoutCreationScreen> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customWorkoutState = ref.watch(customWorkoutProvider);
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: 'Create Custom Workout',
        onBackPressed: () => Navigator.of(context).pop(),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Workout Name Input
                    _buildWorkoutNameSection(theme),
                    const SizedBox(height: 24),
                    
                    // Workout Description Input
                    _buildWorkoutDescriptionSection(theme),
                    const SizedBox(height: 24),
                    
                    // Exercises Section
                    _buildExercisesSection(customWorkoutState, theme),
                    const SizedBox(height: 24),
                    
                    // Add Exercise Button
                    _buildAddExerciseButton(theme),
                    const SizedBox(height: 100), // Space for bottom buttons
                  ],
                ),
              ),
            ),
            
            // Bottom Action Buttons
            _buildBottomActions(customWorkoutState, theme),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutNameSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Workout Name',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            hintText: 'Enter workout name',
            filled: true,
            fillColor: theme.cardColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          style: theme.textTheme.bodyLarge,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a workout name';
            }
            if (value.trim().length < 3) {
              return 'Workout name must be at least 3 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildWorkoutDescriptionSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description (Optional)',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Describe your workout...',
            filled: true,
            fillColor: theme.cardColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          style: theme.textTheme.bodyLarge,
        ),
      ],
    );
  }

  Widget _buildExercisesSection(CustomWorkoutState state, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Exercises',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColorPalette.primaryOrange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${state.exercises.length}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColorPalette.primaryOrange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        if (state.exercises.isEmpty)
          _buildEmptyExercisesState(theme)
        else
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColorPalette.primaryOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColorPalette.primaryOrange.withOpacity(0.2)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      size: 16,
                      color: AppColorPalette.primaryOrange,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Tap "Add Exercise" to add more exercises to your workout',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppColorPalette.primaryOrange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              _buildExercisesList(state.exercises, theme),
            ],
          ),
      ],
    );
  }

  Widget _buildEmptyExercisesState(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.3),
          style: BorderStyle.solid,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.fitness_center_outlined,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No exercises added yet',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap "Add Exercise" below to add exercises one by one',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'You can add multiple exercises to create your custom workout',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildExercisesList(List<CustomWorkoutExercise> exercises, ThemeData theme) {
    return Column(
      children: exercises.asMap().entries.map((entry) {
        final index = entry.key;
        final exercise = entry.value;
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildExerciseCard(exercise, index, theme),
        );
      }).toList(),
    );
  }

  Widget _buildExerciseCard(CustomWorkoutExercise exercise, int index, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Exercise Header
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColorPalette.primaryOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: AppColorPalette.primaryOrange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise.name,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (exercise.description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        exercise.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                onSelected: (value) {
                  if (value == 'edit') {
                    _editExercise(exercise, index);
                  } else if (value == 'delete') {
                    _deleteExercise(index);
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20, color: theme.colorScheme.onSurfaceVariant),
                        const SizedBox(width: 12),
                        const Text('Edit'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: AppColorPalette.error),
                        SizedBox(width: 12),
                        Text('Delete', style: TextStyle(color: AppColorPalette.error)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Exercise Configuration
          Row(
            children: [
              _buildConfigChip('${exercise.sets} sets', Icons.repeat, theme),
              const SizedBox(width: 12),
              _buildConfigChip('${exercise.reps.first} reps', Icons.numbers, theme),
              const SizedBox(width: 12),
              _buildConfigChip('${exercise.weights.first.toInt()}lbs', Icons.fitness_center, theme),
            ],
          ),
          
          if (exercise.restInterval > 0) ...[
            const SizedBox(height: 8),
            _buildConfigChip('${exercise.restInterval}s rest', Icons.timer, theme),
          ],
        ],
      ),
    );
  }

  Widget _buildConfigChip(String label, IconData icon, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: theme.colorScheme.onSurfaceVariant),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigRow(String label, String value, VoidCallback onDecrease, VoidCallback onIncrease) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildControlButton(Icons.remove, onDecrease),
                Expanded(
                  child: Text(
                    value,
                    textAlign: TextAlign.center,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                _buildControlButton(Icons.add, onIncrease),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildControlButton(IconData icon, VoidCallback onPressed) {
    final theme = Theme.of(context);
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: AppColorPalette.primaryOrange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          icon,
          size: 16,
          color: AppColorPalette.primaryOrange,
        ),
      ),
    );
  }

  Widget _buildAddExerciseButton(ThemeData theme) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: _addExercise,
        icon: const Icon(Icons.add, color: AppColorPalette.primaryOrange),
        label: Text(
          'Add Exercise',
          style: theme.textTheme.labelLarge?.copyWith(
            color: AppColorPalette.primaryOrange,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          side: const BorderSide(color: AppColorPalette.primaryOrange),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomActions(CustomWorkoutState state, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: theme.colorScheme.outline.withOpacity(0.1)),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _previewWorkout,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: theme.colorScheme.onSurfaceVariant),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Preview',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: PrimaryButton(
                text: state.isLoading ? 'Saving...' : 'Save Workout',
                onPressed: state.isLoading || state.exercises.isEmpty 
                    ? null 
                    : _saveWorkout,
                isLoading: state.isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addExercise() async {
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => const ExerciseSelectionScreen(),
      ),
    );

    if (result != null) {
      ref.read(customWorkoutProvider.notifier).addExercise(
        exerciseId: result['exercise_id'],
        name: result['name'],
        description: result['description'] ?? '',
        sets: result['sets'] ?? 3,
        reps: result['reps'] ?? [10],
        weights: result['weights'] ?? [0.0],
        restInterval: result['rest_interval'] ?? 60,
      );
      
      // Show success feedback
      _showSuccessSnackBar('${result['name']} added to workout!');
    }
  }

  void _editExercise(CustomWorkoutExercise exercise, int index) {
    // TODO: Implement exercise editing dialog
    _showExerciseConfigDialog(exercise: exercise, index: index);
  }

  void _deleteExercise(int index) {
    ref.read(customWorkoutProvider.notifier).removeExercise(index);
  }

  void _previewWorkout() {
    if (!_formKey.currentState!.validate()) return;
    
    // TODO: Show workout preview dialog
    _showWorkoutPreviewDialog();
  }

  void _saveWorkout() async {
    if (!_formKey.currentState!.validate()) return;
    
    final customWorkoutState = ref.read(customWorkoutProvider);
    if (customWorkoutState.exercises.isEmpty) {
      _showErrorSnackBar('Please add at least one exercise');
      return;
    }

    try {
      await ref.read(customWorkoutProvider.notifier).saveWorkout(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
      );

      if (mounted) {
        _showSuccessSnackBar('Workout saved successfully!');
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to save workout: ${e.toString()}');
      }
    }
  }

  void _showExerciseConfigDialog({CustomWorkoutExercise? exercise, int? index}) {
    int sets = exercise?.sets ?? 3;
    List<int> reps = List.from(exercise?.reps ?? [10]);
    List<double> weights = List.from(exercise?.weights ?? [0.0]);
    int restInterval = exercise?.restInterval ?? 60;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(exercise == null ? 'Configure Exercise' : 'Edit ${exercise.name}'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Sets Configuration
                _buildConfigRow(
                  'Sets',
                  sets.toString(),
                  () => setDialogState(() => sets = (sets - 1).clamp(1, 10)),
                  () => setDialogState(() => sets = (sets + 1).clamp(1, 10)),
                ),
                const SizedBox(height: 16),
                
                // Reps Configuration
                _buildConfigRow(
                  'Reps',
                  reps.first.toString(),
                  () => setDialogState(() => reps = [(reps.first - 1).clamp(1, 50)]),
                  () => setDialogState(() => reps = [(reps.first + 1).clamp(1, 50)]),
                ),
                const SizedBox(height: 16),
                
                // Weight Configuration
                _buildConfigRow(
                  'Weight (lbs)',
                  weights.first.toInt().toString(),
                  () => setDialogState(() => weights = [(weights.first - 5).clamp(0, 1000)]),
                  () => setDialogState(() => weights = [(weights.first + 5).clamp(0, 1000)]),
                ),
                const SizedBox(height: 16),
                
                // Rest Interval Configuration
                _buildConfigRow(
                  'Rest (seconds)',
                  restInterval.toString(),
                  () => setDialogState(() => restInterval = (restInterval - 15).clamp(0, 300)),
                  () => setDialogState(() => restInterval = (restInterval + 15).clamp(0, 300)),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (exercise != null && index != null) {
                  // Update existing exercise
                  final updatedExercise = exercise.copyWith(
                    sets: sets,
                    reps: reps,
                    weights: weights,
                    restInterval: restInterval,
                  );
                  ref.read(customWorkoutProvider.notifier).updateExercise(index, updatedExercise);
                }
                Navigator.of(context).pop();
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showWorkoutPreviewDialog() {
    final customWorkoutState = ref.read(customWorkoutProvider);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Workout Preview'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Name: ${_nameController.text}'),
            if (_descriptionController.text.isNotEmpty)
              Text('Description: ${_descriptionController.text}'),
            const SizedBox(height: 16),
            Text('Exercises: ${customWorkoutState.exercises.length}'),
            ...customWorkoutState.exercises.map((ex) => 
              Text('• ${ex.name} - ${ex.sets} sets')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColorPalette.successGreen,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColorPalette.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
} 