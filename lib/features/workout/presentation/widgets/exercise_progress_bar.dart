import 'package:flutter/material.dart';
import '../../../../core/theme/color_palette.dart';
import '../../domain/models/workout_session.dart';

class ExerciseProgressBar extends StatefulWidget {
  final List<WorkoutExercise> exercises;
  final int currentIndex;
  final double height;

  const ExerciseProgressBar({
    super.key,
    required this.exercises,
    required this.currentIndex,
    this.height = 4,
  });

  @override
  State<ExerciseProgressBar> createState() => _ExerciseProgressBarState();
}

class _ExerciseProgressBarState extends State<ExerciseProgressBar>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _controllers = List.generate(
      widget.exercises.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 300 + (index * 100)),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeInOut,
        ),
      );
    }).toList();
  }

  void _startAnimations() {
    for (int i = 0; i <= widget.currentIndex && i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void didUpdateWidget(ExerciseProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.currentIndex != widget.currentIndex) {
      _updateProgress();
    }
  }

  void _updateProgress() {
    for (int i = 0; i < _controllers.length; i++) {
      if (i <= widget.currentIndex) {
        _controllers[i].forward();
      } else {
        _controllers[i].reset();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Progress segments
        Row(
          children: widget.exercises.asMap().entries.map((entry) {
            final index = entry.key;
            final exercise = entry.value;
            
            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  right: index < widget.exercises.length - 1 ? 4 : 0,
                ),
                child: _buildProgressSegment(index, exercise),
              ),
            );
          }).toList(),
        ),
        
        const SizedBox(height: 8),
        
        // Exercise labels
        Row(
          children: widget.exercises.asMap().entries.map((entry) {
            final index = entry.key;
            final exercise = entry.value;
            
            return Expanded(
              child: _buildExerciseLabel(index, exercise),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildProgressSegment(int index, WorkoutExercise exercise) {
    final isCompleted = index < widget.currentIndex;
    final isCurrent = index == widget.currentIndex;
    final isPending = index > widget.currentIndex;

    Color segmentColor;
    if (isCompleted) {
      segmentColor = AppColorPalette.successGreen;
    } else if (isCurrent) {
      segmentColor = AppColorPalette.primaryOrange;
    } else {
      segmentColor = Colors.white.withOpacity(0.2);
    }

    return AnimatedBuilder(
      animation: _animations[index],
      builder: (context, child) {
        return Container(
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.height / 2),
            color: Colors.white.withOpacity(0.1),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: isPending ? 0.0 : _animations[index].value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.height / 2),
                color: segmentColor,
                boxShadow: isCurrent ? [
                  BoxShadow(
                    color: segmentColor.withOpacity(0.5),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ] : null,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildExerciseLabel(int index, WorkoutExercise exercise) {
    final isCompleted = index < widget.currentIndex;
    final isCurrent = index == widget.currentIndex;
    final isPending = index > widget.currentIndex;

    Color textColor;
    FontWeight fontWeight;
    
    if (isCompleted) {
      textColor = AppColorPalette.successGreen;
      fontWeight = FontWeight.w600;
    } else if (isCurrent) {
      textColor = Colors.white;
      fontWeight = FontWeight.w700;
    } else {
      textColor = Colors.white.withOpacity(0.5);
      fontWeight = FontWeight.w500;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        children: [
          // Exercise number
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: isCompleted 
                  ? AppColorPalette.successGreen
                  : isCurrent 
                      ? AppColorPalette.primaryOrange
                      : Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Center(
              child: isCompleted
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 12,
                    )
                  : Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
            ),
          ),
          
          const SizedBox(height: 4),
          
          // Exercise name (abbreviated)
          Text(
            _abbreviateExerciseName(exercise.name),
            style: TextStyle(
              color: textColor,
              fontSize: 10,
              fontWeight: fontWeight,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _abbreviateExerciseName(String name) {
    // Split by spaces and take first letters of each word
    final words = name.split(' ');
    if (words.length == 1) {
      return words.first.length > 6 ? '${words.first.substring(0, 6)}...' : words.first;
    }
    
    // For multiple words, create abbreviation
    if (words.length <= 3) {
      return words.map((word) => word.isNotEmpty ? word[0].toUpperCase() : '').join('');
    }
    
    // For longer names, take first 2 words
    return words.take(2).map((word) => word.isNotEmpty ? word[0].toUpperCase() : '').join('');
  }
}

/// Circular progress indicator for individual exercises
class CircularExerciseProgress extends StatefulWidget {
  final WorkoutExercise exercise;
  final double size;
  final Color? progressColor;
  final Color? backgroundColor;

  const CircularExerciseProgress({
    super.key,
    required this.exercise,
    this.size = 60,
    this.progressColor,
    this.backgroundColor,
  });

  @override
  State<CircularExerciseProgress> createState() => _CircularExerciseProgressState();
}

class _CircularExerciseProgressState extends State<CircularExerciseProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: widget.exercise.completionPercentage,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _controller.forward();
  }

  @override
  void didUpdateWidget(CircularExerciseProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.exercise.completionPercentage != widget.exercise.completionPercentage) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.exercise.completionPercentage,
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Curves.easeInOut,
        ),
      );
      
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progressColor = widget.progressColor ?? AppColorPalette.primaryOrange;
    final backgroundColor = widget.backgroundColor ?? Colors.white.withOpacity(0.2);

    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background circle
          SizedBox(
            width: widget.size,
            height: widget.size,
            child: CircularProgressIndicator(
              value: 1.0,
              strokeWidth: 4,
              backgroundColor: backgroundColor,
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.transparent),
            ),
          ),
          
          // Progress circle
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  value: _animation.value,
                  strokeWidth: 4,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                ),
              );
            },
          ),
          
          // Center content
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${widget.exercise.completedSets}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: widget.size * 0.25,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Text(
                '/${widget.exercise.sets}',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: widget.size * 0.15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
