import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/workout_session.dart';

class ExerciseListItem extends StatefulWidget {
  final WorkoutExercise exercise;
  final int exerciseNumber;
  final VoidCallback? onTap;
  final bool showProgress;

  const ExerciseListItem({
    super.key,
    required this.exercise,
    required this.exerciseNumber,
    this.onTap,
    this.showProgress = false,
  });

  @override
  State<ExerciseListItem> createState() => _ExerciseListItemState();
}

class _ExerciseListItemState extends State<ExerciseListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GlassMorphismCard(
            onTap: widget.onTap != null ? _handleTap : null,
            enableHapticFeedback: true,
            child: Row(
              children: [
                _buildThumbnail(theme),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildExerciseInfo(theme),
                ),
                _buildPlayButton(theme),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildThumbnail(ThemeData theme) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: theme.colorScheme.surfaceContainerHighest,
      ),
      child: Stack(
        children: [
          // Thumbnail image or placeholder
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: widget.exercise.thumbnailUrl != null
                ? Image.network(
                    widget.exercise.thumbnailUrl!,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => _buildPlaceholderThumbnail(theme),
                  )
                : _buildPlaceholderThumbnail(theme),
          ),
          
          // Exercise number badge
          Positioned(
            top: 6,
            left: 6,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: AppColorPalette.primaryOrange,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  widget.exerciseNumber.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ),
          ),
          
          // Progress indicator (if showing progress)
          if (widget.showProgress && widget.exercise.isCompleted)
            Positioned(
              bottom: 6,
              right: 6,
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: AppColorPalette.successGreen,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderThumbnail(ThemeData theme) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColorPalette.primaryOrange.withOpacity(0.3),
            AppColorPalette.primaryOrangeLight.withOpacity(0.3),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Icon(
        Icons.fitness_center,
        color: AppColorPalette.primaryOrange,
        size: 32,
      ),
    );
  }

  Widget _buildExerciseInfo(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Exercise name
        Text(
          widget.exercise.name,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: theme.colorScheme.onSurface,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        const SizedBox(height: 4),
        
        // Exercise details
        Text(
          _buildExerciseDetails(),
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Progress or sets info
        if (widget.showProgress)
          _buildProgressInfo(theme)
        else
          _buildSetsInfo(theme),
      ],
    );
  }

  Widget _buildPlayButton(ThemeData theme) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: AppColorPalette.primaryOrange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: const Icon(
        Icons.play_arrow,
        color: AppColorPalette.primaryOrange,
        size: 24,
      ),
    );
  }

  Widget _buildProgressInfo(ThemeData theme) {
    final completedSets = widget.exercise.completedSets;
    final totalSets = widget.exercise.sets;
    final progress = completedSets / totalSets;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(
                  widget.exercise.isCompleted
                      ? AppColorPalette.successGreen
                      : AppColorPalette.primaryOrange,
                ),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '$completedSets/$totalSets',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          widget.exercise.isCompleted ? 'Completed' : widget.exercise.progressText,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: widget.exercise.isCompleted
                ? AppColorPalette.successGreen
                : theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildSetsInfo(ThemeData theme) {
    return Row(
      children: [
        _buildInfoChip(
          icon: Icons.repeat,
          text: widget.exercise.setsText,
          theme: theme,
        ),
        const SizedBox(width: 8),
        _buildInfoChip(
          icon: Icons.timer_outlined,
          text: widget.exercise.restIntervalText,
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String text,
    required ThemeData theme,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  String _buildExerciseDetails() {
    final reps = widget.exercise.targetReps.isNotEmpty 
        ? widget.exercise.targetReps.first 
        : 10;
    final weight = widget.exercise.currentWeightText;
    
    return '$reps reps • $weight';
  }

  void _handleTap() {
    HapticFeedback.lightImpact();
    _controller.forward().then((_) {
      _controller.reverse();
    });
    widget.onTap?.call();
  }
}
