import 'exercise_set.dart';

enum ExerciseStatus { 
  pending, 
  inProgress, 
  resting, 
  completed 
}

class Exercise {
  final String name;
  final int targetSets;
  final int targetReps;
  final List<ExerciseSet> completedSets;
  int currentSetIndex;
  ExerciseStatus status;
  DateTime? restStartTime;
  int? restDurationSeconds;

  Exercise({
    required this.name,
    required this.targetSets,
    required this.targetReps,
    List<ExerciseSet>? completedSets,
    this.currentSetIndex = 0,
    this.status = ExerciseStatus.pending,
    this.restStartTime,
    this.restDurationSeconds,
  }) : completedSets = completedSets ?? [];

  bool get isCompleted => completedSets.length >= targetSets;
  
  int get currentSetNumber => completedSets.length + 1;
  
  int get remainingSets => (targetSets - completedSets.length).clamp(0, targetSets);
  
  ExerciseSet? get currentSet => 
      currentSetIndex < completedSets.length 
          ? completedSets[currentSetIndex] 
          : null;

  int get totalRepsCompleted => completedSets
      .map((s) => s.reps)
      .fold<int>(0, (a, b) => a + b);

  bool get isResting => status == ExerciseStatus.resting && restStartTime != null;

  Duration? get remainingRestTime {
    if (!isResting || restDurationSeconds == null) return null;
    
    final elapsed = DateTime.now().difference(restStartTime!);
    final remaining = Duration(seconds: restDurationSeconds!) - elapsed;
    return remaining.isNegative ? Duration.zero : remaining;
  }

  void startRest(int durationSeconds) {
    status = ExerciseStatus.resting;
    restStartTime = DateTime.now();
    restDurationSeconds = durationSeconds;
  }

  void endRest() {
    status = ExerciseStatus.inProgress;
    restStartTime = null;
    restDurationSeconds = null;
  }

  void addSet(ExerciseSet set) {
    completedSets.add(set);
    if (isCompleted) {
      status = ExerciseStatus.completed;
    }
  }

  Map<String, dynamic> toJson() => {
    'name': name,
    'targetSets': targetSets,
    'targetReps': targetReps,
    'completedSets': completedSets.map((s) => s.toJson()).toList(),
    'currentSetIndex': currentSetIndex,
    'status': status.name,
    'restStartTime': restStartTime?.toIso8601String(),
    'restDurationSeconds': restDurationSeconds,
  };

  factory Exercise.fromJson(Map<String, dynamic> json) => Exercise(
    name: json['name'],
    targetSets: json['targetSets'],
    targetReps: json['targetReps'],
    completedSets: (json['completedSets'] as List?)
        ?.map((s) => ExerciseSet.fromJson(s))
        .toList(),
    currentSetIndex: json['currentSetIndex'] ?? 0,
    status: ExerciseStatus.values.byName(json['status'] ?? 'pending'),
    restStartTime: json['restStartTime'] != null 
        ? DateTime.parse(json['restStartTime']) 
        : null,
    restDurationSeconds: json['restDurationSeconds'],
  );
} 