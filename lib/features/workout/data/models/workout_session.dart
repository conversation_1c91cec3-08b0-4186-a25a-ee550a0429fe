import 'exercise.dart';

enum WorkoutStatus { 
  notStarted, 
  inProgress, 
  resting, 
  completed, 
  paused 
}

class WorkoutSession {
  final String id;
  final String name;
  final DateTime startTime;
  DateTime? endTime;
  final List<Exercise> exercises;
  int currentExerciseIndex;
  WorkoutStatus status;
  
  WorkoutSession({
    required this.id,
    required this.name,
    required this.startTime,
    this.endTime,
    required this.exercises,
    this.currentExerciseIndex = 0,
    this.status = WorkoutStatus.notStarted,
  });

  Exercise? get currentExercise => 
      currentExerciseIndex < exercises.length 
          ? exercises[currentExerciseIndex] 
          : null;

  bool get isCompleted => currentExerciseIndex >= exercises.length;

  int get totalSetsCompleted => exercises
      .expand((e) => e.completedSets)
      .length;

  int get totalRepsCompleted => exercises
      .expand((e) => e.completedSets)
      .map((s) => s.reps)
      .fold<int>(0, (a, b) => a + b);

  Duration get elapsedTime => endTime != null 
      ? endTime!.difference(startTime)
      : DateTime.now().difference(startTime);

  double get progressPercentage {
    final totalTargetSets = exercises
        .map((e) => e.targetSets)
        .fold(0, (a, b) => a + b);
    return totalTargetSets > 0 
        ? totalSetsCompleted / totalTargetSets 
        : 0.0;
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'startTime': startTime.toIso8601String(),
    'endTime': endTime?.toIso8601String(),
    'exercises': exercises.map((e) => e.toJson()).toList(),
    'currentExerciseIndex': currentExerciseIndex,
    'status': status.name,
  };

  factory WorkoutSession.fromJson(Map<String, dynamic> json) => WorkoutSession(
    id: json['id'],
    name: json['name'],
    startTime: DateTime.parse(json['startTime']),
    endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
    exercises: (json['exercises'] as List)
        .map((e) => Exercise.fromJson(e))
        .toList(),
    currentExerciseIndex: json['currentExerciseIndex'] ?? 0,
    status: WorkoutStatus.values.byName(json['status'] ?? 'notStarted'),
  );
} 