class ExerciseSet {
  final int reps;
  final DateTime startTime;
  final DateTime endTime;
  final Duration? restDuration;
  final String? notes;

  ExerciseSet({
    required this.reps,
    required this.startTime,
    required this.endTime,
    this.restDuration,
    this.notes,
  });

  Duration get duration => endTime.difference(startTime);

  Map<String, dynamic> toJson() => {
    'reps': reps,
    'startTime': startTime.toIso8601String(),
    'endTime': endTime.toIso8601String(),
    'restDuration': restDuration?.inSeconds,
    'notes': notes,
  };

  factory ExerciseSet.fromJson(Map<String, dynamic> json) => ExerciseSet(
    reps: json['reps'],
    startTime: DateTime.parse(json['startTime']),
    endTime: DateTime.parse(json['endTime']),
    restDuration: json['restDuration'] != null 
        ? Duration(seconds: json['restDuration']) 
        : null,
    notes: json['notes'],
  );

  // Factory for creating a set that's currently in progress
  factory ExerciseSet.inProgress({
    required int reps,
    DateTime? startTime,
  }) => ExerciseSet(
    reps: reps,
    startTime: startTime ?? DateTime.now(),
    endTime: DateTime.now(), // Will be updated when set is completed
  );

  // Create a copy with updated values
  ExerciseSet copyWith({
    int? reps,
    DateTime? startTime,
    DateTime? endTime,
    Duration? restDuration,
    String? notes,
  }) => ExerciseSet(
    reps: reps ?? this.reps,
    startTime: startTime ?? this.startTime,
    endTime: endTime ?? this.endTime,
    restDuration: restDuration ?? this.restDuration,
    notes: notes ?? this.notes,
  );
} 