import 'package:flutter/material.dart';

/// Enum representing the status of the voice assistant.
enum VoiceStatus {
  /// Not connected to the voice service.
  disconnected,

  /// Connected and ready, but not actively listening or speaking.
  connected,

  /// Actively listening for user input.
  listening,

  /// The assistant is speaking.
  speaking,

  /// An error has occurred.
  error;

  /// Returns the corresponding color for the status.
  Color get color {
    switch (this) {
      case VoiceStatus.disconnected:
        return Colors.red;
      case VoiceStatus.connected:
        return Colors.white24;
      case VoiceStatus.listening:
        return Colors.green;
      case VoiceStatus.speaking:
        return const Color(0xFFF97316); // AppColorPalette.primaryOrange
      case VoiceStatus.error:
        return Colors.red.shade800;
    }
  }

  /// Returns the corresponding icon for the status.
  IconData get icon {
    switch (this) {
      case VoiceStatus.disconnected:
        return Icons.wifi_off;
      case VoiceStatus.connected:
        return Icons.mic_none;
      case VoiceStatus.listening:
        return Icons.mic;
      case VoiceStatus.speaking:
        return Icons.record_voice_over;
      case VoiceStatus.error:
        return Icons.error_outline;
    }
  }

  /// Returns a descriptive string for the status.
  String get label {
    switch (this) {
      case VoiceStatus.disconnected:
        return 'Connecting...';
      case VoiceStatus.connected:
        return 'Ready';
      case VoiceStatus.listening:
        return 'Listening';
      case VoiceStatus.speaking:
        return 'Coach Speaking';
      case VoiceStatus.error:
        return 'Error';
    }
  }
} 