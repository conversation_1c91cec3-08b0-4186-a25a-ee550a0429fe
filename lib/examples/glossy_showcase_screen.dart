import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/theme/color_palette.dart';
import '../core/theme/spacing.dart';
import '../core/theme/glass_morphism.dart';
import '../shared/widgets/enhanced_glass_morphism_card.dart';

/// Showcase screen demonstrating the new Glossy package integration
/// with the navy/cream theme
class GlossyShowcaseScreen extends ConsumerWidget {
  const GlossyShowcaseScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColorPalette.bgPrimary,
      appBar: AppBar(
        title: const Text(
          'Glossy Effects Showcase',
          style: TextStyle(
            color: AppColorPalette.textCreamPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(
          color: AppColorPalette.textCreamPrimary,
        ),
      ),
      body: SingleChildScrollView(
        padding: AppSpacing.paddingMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildSectionHeader(context, 'Enhanced Glass Cards'),
            const SizedBox(height: 16),
            
            // Basic Glass Container
            GlassMorphism.glassContainer(
              child: const Column(
                children: [
                  Text(
                    'Basic Glass Container',
                    style: TextStyle(
                      color: AppColorPalette.textCreamPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Using the enhanced Glossy package with navy theme integration.',
                    style: TextStyle(
                      color: AppColorPalette.textCreamSecondary,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Premium Glass Card
            GlassMorphism.premiumGlassCard(
              child: const Column(
                children: [
                  Icon(
                    Icons.star,
                    color: AppColorPalette.colorAccentGold,
                    size: 32,
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Premium Glass Card',
                    style: TextStyle(
                      color: AppColorPalette.textCreamPrimary,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Enhanced with stronger blur effects and premium styling.',
                    style: TextStyle(
                      color: AppColorPalette.textCreamSecondary,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            _buildSectionHeader(context, 'Workout Cards'),
            const SizedBox(height: 16),
            
            // Workout Cards Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.2,
              children: [
                GlossyStatsCard(
                  label: 'Workouts',
                  value: '24',
                  icon: Icons.fitness_center,
                  iconColor: AppColorPalette.colorNavyBright,
                  onTap: () => _showSnackBar(context, 'Workouts tapped'),
                ),
                GlossyStatsCard(
                  label: 'Calories',
                  value: '1.2k',
                  icon: Icons.local_fire_department,
                  iconColor: AppColorPalette.colorAccentOrange,
                  onTap: () => _showSnackBar(context, 'Calories tapped'),
                ),
                GlossyStatsCard(
                  label: 'Minutes',
                  value: '180',
                  icon: Icons.timer,
                  iconColor: AppColorPalette.colorAccentMint,
                  onTap: () => _showSnackBar(context, 'Minutes tapped'),
                ),
                GlossyStatsCard(
                  label: 'Streak',
                  value: '7',
                  icon: Icons.whatshot,
                  iconColor: AppColorPalette.colorAccentGold,
                  onTap: () => _showSnackBar(context, 'Streak tapped'),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            _buildSectionHeader(context, 'Interactive Components'),
            const SizedBox(height: 16),
            
            // Workout Card Example
            GlossyWorkoutCard(
              title: 'Upper Body Strength',
              subtitle: '45 min • 8 exercises',
              leading: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: AppColorPalette.colorNavyBright.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.fitness_center,
                  color: AppColorPalette.colorNavyBright,
                ),
              ),
              trailing: const Icon(
                Icons.play_arrow,
                color: AppColorPalette.colorAccentOrange,
                size: 28,
              ),
              onTap: () => _showSnackBar(context, 'Workout card tapped'),
            ),
            
            const SizedBox(height: 24),
            
            // Buttons Section
            Row(
              children: [
                Expanded(
                  child: GlossyButton(
                    text: 'Start Workout',
                    backgroundColor: AppColorPalette.colorAccentOrange,
                    icon: const Icon(
                      Icons.play_arrow,
                      color: AppColorPalette.colorCreamWhite,
                    ),
                    onPressed: () => _showSnackBar(context, 'Start Workout pressed'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: GlossyButton(
                    text: 'View Stats',
                    backgroundColor: AppColorPalette.colorNavyBright,
                    icon: const Icon(
                      Icons.analytics,
                      color: AppColorPalette.colorCreamWhite,
                    ),
                    onPressed: () => _showSnackBar(context, 'View Stats pressed'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            _buildSectionHeader(context, 'Special Components'),
            const SizedBox(height: 16),
            
            // Modal Example Button
            Center(
              child: GlossyButton(
                text: 'Show Glossy Modal',
                width: 200,
                backgroundColor: AppColorPalette.colorAccentMint,
                onPressed: () => _showGlossyModal(context),
              ),
            ),
            
            const SizedBox(height: 100), // Bottom padding
          ],
        ),
      ),
      
      // Floating Action Button with Glossy Effect
      floatingActionButton: GlassMorphism.glossyFAB(
        onPressed: () => _showSnackBar(context, 'Glossy FAB pressed'),
        child: const Icon(
          Icons.add,
          color: AppColorPalette.colorCreamWhite,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        color: AppColorPalette.textCreamPrimary,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColorPalette.colorNavyBright,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showGlossyModal(BuildContext context) {
    showDialog(
      context: context,
      barrierColor: AppColorPalette.colorBlackPure.withOpacity(0.7),
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: GlassMorphism.glossyModal(
          width: 300,
          height: 250,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.star,
                color: AppColorPalette.colorAccentGold,
                size: 48,
              ),
              const SizedBox(height: 16),
              const Text(
                'Glossy Modal',
                style: TextStyle(
                  color: AppColorPalette.textCreamPrimary,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'This modal uses the enhanced glass morphism effects with the navy theme.',
                style: TextStyle(
                  color: AppColorPalette.textCreamSecondary,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              GlossyButton(
                text: 'Close',
                width: 120,
                height: 40,
                backgroundColor: AppColorPalette.colorNavyBright,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
