import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/theme/app_style_manager.dart';
import '../core/theme/color_palette.dart';

/// Navy Theme Showcase - Demonstrates the new military-inspired design
/// Features the Liberator font and navy color palette
class NavyThemeShowcase extends ConsumerWidget {
  const NavyThemeShowcase({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColorPalette.bgPrimary,
      appBar: AppBar(
        title: const Text(
          'NAVY THEME SHOWCASE',
          style: TextStyle(
            fontFamily: 'Liberator',
            fontWeight: FontWeight.w700,
            letterSpacing: 1.2,
          ),
        ),
        backgroundColor: AppColorPalette.bgSurface,
        foregroundColor: AppColorPalette.textPrimary,
      ),
      body: SingleChildScrollView(
        padding: AppStyleManager.standardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ==================== HERO SECTION ====================
            _buildHeroSection(context),
            const SizedBox(height: 32),

            // ==================== COLOR PALETTE ====================
            _buildColorPalette(context),
            const SizedBox(height: 32),

            // ==================== TYPOGRAPHY ====================
            _buildTypographySection(context),
            const SizedBox(height: 32),

            // ==================== BUTTONS ====================
            _buildButtonSection(context),
            const SizedBox(height: 32),

            // ==================== CARDS ====================
            _buildCardSection(context),
            const SizedBox(height: 32),

            // ==================== INPUTS ====================
            _buildInputSection(context),
            const SizedBox(height: 32),

            // ==================== NOTIFICATIONS ====================
            _buildNotificationSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColorPalette.bgSurface,
            AppColorPalette.bgElevated,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColorPalette.borderGlass,
          width: 1,
        ),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'TACTICAL FITNESS',
            style: TextStyle(
              fontFamily: 'Liberator',
              fontSize: 32,
              fontWeight: FontWeight.w700,
              color: AppColorPalette.textPrimary,
              letterSpacing: 2.0,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Military-Inspired Design System',
            style: TextStyle(
              fontFamily: 'Liberator',
              fontSize: 16,
              fontWeight: FontWeight.w300,
              color: AppColorPalette.textSecondary,
              letterSpacing: 1.0,
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.military_tech,
                color: AppColorPalette.accentGold,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'PRECISION • DISCIPLINE • EXCELLENCE',
                style: TextStyle(
                  fontFamily: 'Liberator',
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColorPalette.accentGold,
                  letterSpacing: 1.5,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorPalette(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'COLOR PALETTE',
          style: TextStyle(
            fontFamily: 'Liberator',
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColorPalette.textPrimary,
            letterSpacing: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildColorSwatch('PRIMARY', AppColorPalette.bgPrimary),
            _buildColorSwatch('SURFACE', AppColorPalette.bgSurface),
            _buildColorSwatch('ELEVATED', AppColorPalette.bgElevated),
            _buildColorSwatch('NAVY', AppColorPalette.accentPrimary),
            _buildColorSwatch('ORANGE', AppColorPalette.accentSecondary),
            _buildColorSwatch('BRIGHT', AppColorPalette.accentBright),
            _buildColorSwatch('GOLD', AppColorPalette.accentGold),
            _buildColorSwatch('SUCCESS', AppColorPalette.successGreen),
            _buildColorSwatch('WARNING', AppColorPalette.warning),
            _buildColorSwatch('ERROR', AppColorPalette.error),
          ],
        ),
      ],
    );
  }

  Widget _buildColorSwatch(String name, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColorPalette.borderGlass,
              width: 1,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          name,
          style: const TextStyle(
            fontFamily: 'Liberator',
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: AppColorPalette.textTertiary,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }

  Widget _buildTypographySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'TYPOGRAPHY',
          style: TextStyle(
            fontFamily: 'Liberator',
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColorPalette.textPrimary,
            letterSpacing: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: AppStyleManager.cardDecoration(context),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'LIBERATOR HEAVY',
                style: TextStyle(
                  fontFamily: 'Liberator',
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  color: AppColorPalette.textPrimary,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Liberator Medium',
                style: TextStyle(
                  fontFamily: 'Liberator',
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: AppColorPalette.textSecondary,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Liberator Light',
                style: TextStyle(
                  fontFamily: 'Liberator',
                  fontSize: 16,
                  fontWeight: FontWeight.w300,
                  color: AppColorPalette.textTertiary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildButtonSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'BUTTONS',
          style: TextStyle(
            fontFamily: 'Liberator',
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColorPalette.textPrimary,
            letterSpacing: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: () {},
          style: AppStyleManager.primaryButton(context),
          child: const Text('PRIMARY MISSION'),
        ),
        const SizedBox(height: 12),
        OutlinedButton(
          onPressed: () {},
          style: AppStyleManager.secondaryButton(context),
          child: const Text('SECONDARY OBJECTIVE'),
        ),
        const SizedBox(height: 12),
        ElevatedButton(
          onPressed: () {},
          style: AppStyleManager.dangerButton(context),
          child: const Text('ABORT MISSION'),
        ),
      ],
    );
  }

  Widget _buildCardSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'TACTICAL CARDS',
          style: TextStyle(
            fontFamily: 'Liberator',
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColorPalette.textPrimary,
            letterSpacing: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: AppStyleManager.workoutCardDecoration(context),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.fitness_center,
                    color: AppColorPalette.accentGold,
                    size: 24,
                  ),
                  SizedBox(width: 12),
                  Text(
                    'MISSION BRIEFING',
                    style: TextStyle(
                      fontFamily: 'Liberator',
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: AppColorPalette.textPrimary,
                      letterSpacing: 1.0,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                'Execute tactical fitness protocols with military precision. Maintain operational readiness through disciplined training regimens.',
                style: TextStyle(
                  fontFamily: 'Liberator',
                  fontSize: 14,
                  fontWeight: FontWeight.w300,
                  color: AppColorPalette.textSecondary,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInputSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'COMMAND INPUT',
          style: TextStyle(
            fontFamily: 'Liberator',
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColorPalette.textPrimary,
            letterSpacing: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        TextField(
          decoration: AppStyleManager.inputDecoration(
            context,
            labelText: 'Mission Code',
            hintText: 'Enter tactical designation',
            prefixIcon: const Icon(Icons.security),
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'TACTICAL ALERTS',
          style: TextStyle(
            fontFamily: 'Liberator',
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: AppColorPalette.textPrimary,
            letterSpacing: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: () => AppStyleManager.showSuccess(context, 'Mission accomplished!'),
          style: AppStyleManager.primaryButton(context),
          child: const Text('SUCCESS ALERT'),
        ),
        const SizedBox(height: 8),
        ElevatedButton(
          onPressed: () => AppStyleManager.showError(context, 'Mission compromised!'),
          style: AppStyleManager.dangerButton(context),
          child: const Text('ERROR ALERT'),
        ),
      ],
    );
  }
}
