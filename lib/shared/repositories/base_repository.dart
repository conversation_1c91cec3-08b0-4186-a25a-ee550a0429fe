import 'package:flutter/foundation.dart';
import '../services/supabase_service.dart';
import '../../core/errors/error_handler.dart';
import '../../core/utils/validation_utils.dart';

/// Base repository with optimized query patterns and caching
abstract class BaseRepository {
  /// Cache duration for different data types
  static const Duration shortCache = Duration(minutes: 2);
  static const Duration mediumCache = Duration(minutes: 5);
  static const Duration longCache = Duration(minutes: 15);

  /// Execute a cached query with error handling
  Future<T> executeCachedQuery<T>(
    String cacheKey,
    Future<T> Function() queryFunction, {
    Duration? cacheExpiry,
    bool forceRefresh = false,
    String? operation,
  }) async {
    try {
      return await SupabaseService.cachedQuery<T>(
        cacheKey,
        queryFunction,
        customExpiry: cacheExpiry ?? mediumCache,
        forceRefresh: forceRefresh,
      );
    } catch (e) {
      final exception = ErrorHandler.handleDatabaseError(e, operation: operation);
      ErrorHandler.logError(exception, context: runtimeType.toString());
      throw exception;
    }
  }

  /// Execute a paginated query with optimization
  Future<List<T>> executePaginatedQuery<T>(
    String baseQuery,
    String tableName,
    Map<String, dynamic> filters,
    T Function(Map<String, dynamic>) fromJson, {
    int limit = 20,
    int offset = 0,
    String orderBy = 'created_at',
    bool ascending = false,
    Duration? cacheExpiry,
  }) async {
    final cacheKey = _buildCacheKey(tableName, filters, limit, offset, orderBy);

    return executeCachedQuery(
      cacheKey,
      () async {
        var query = SupabaseService.client
            .from(tableName)
            .select(baseQuery);

        // Apply filters first
        for (final entry in filters.entries) {
          if (entry.value != null) {
            query = query.eq(entry.key, entry.value);
          }
        }

        // Then apply ordering and pagination
        final response = await query
            .order(orderBy, ascending: ascending)
            .range(offset, offset + limit - 1);

        return response.map<T>((item) => fromJson(item)).toList();
      },
      cacheExpiry: cacheExpiry,
      operation: 'paginated query on $tableName',
    );
  }

  /// Execute an optimized single record query
  Future<T?> executeSingleQuery<T>(
    String selectQuery,
    String tableName,
    Map<String, dynamic> filters,
    T Function(Map<String, dynamic>) fromJson, {
    Duration? cacheExpiry,
    bool forceRefresh = false,
  }) async {
    final cacheKey = _buildCacheKey('${tableName}_single', filters);

    return executeCachedQuery(
      cacheKey,
      () async {
        var query = SupabaseService.client
            .from(tableName)
            .select(selectQuery);

        // Apply filters
        for (final entry in filters.entries) {
          if (entry.value != null) {
            query = query.eq(entry.key, entry.value);
          }
        }

        final response = await query.maybeSingle();
        return response != null ? fromJson(response) : null;
      },
      cacheExpiry: cacheExpiry ?? BaseRepository.shortCache,
      forceRefresh: forceRefresh,
      operation: 'single query on $tableName',
    );
  }

  /// Execute an upsert operation with cache invalidation
  Future<void> executeUpsert(
    String tableName,
    Map<String, dynamic> data, {
    List<String>? cacheKeysToInvalidate,
    String? operation,
  }) async {
    try {
      // Validate data before upsert
      _validateUpsertData(data);
      
      await SupabaseService.client
          .from(tableName)
          .upsert(data);

      // Invalidate related cache entries
      if (cacheKeysToInvalidate != null) {
        for (final key in cacheKeysToInvalidate) {
          SupabaseService.clearCache(key);
        }
      }
      
      debugPrint('✅ Upsert successful for $tableName');
    } catch (e) {
      final exception = ErrorHandler.handleDatabaseError(e, operation: operation);
      ErrorHandler.logError(exception, context: runtimeType.toString());
      throw exception;
    }
  }

  /// Execute a delete operation with cache invalidation
  Future<void> executeDelete(
    String tableName,
    Map<String, dynamic> filters, {
    List<String>? cacheKeysToInvalidate,
    String? operation,
  }) async {
    try {
      var query = SupabaseService.client.from(tableName).delete();

      // Apply filters
      for (final entry in filters.entries) {
        if (entry.value != null) {
          query = query.eq(entry.key, entry.value);
        }
      }

      await query;

      // Invalidate related cache entries
      if (cacheKeysToInvalidate != null) {
        for (final key in cacheKeysToInvalidate) {
          SupabaseService.clearCache(key);
        }
      }

      debugPrint('✅ Delete successful for $tableName');
    } catch (e) {
      final exception = ErrorHandler.handleDatabaseError(e, operation: operation);
      ErrorHandler.logError(exception, context: runtimeType.toString());
      throw exception;
    }
  }

  /// Build a consistent cache key
  String _buildCacheKey(String prefix, Map<String, dynamic> params, [int? limit, int? offset, String? orderBy]) {
    final keyParts = [prefix];
    
    // Add sorted parameters
    final sortedKeys = params.keys.toList()..sort();
    for (final key in sortedKeys) {
      if (params[key] != null) {
        keyParts.add('${key}_${params[key]}');
      }
    }
    
    if (limit != null) keyParts.add('limit_$limit');
    if (offset != null) keyParts.add('offset_$offset');
    if (orderBy != null) keyParts.add('order_$orderBy');
    
    return keyParts.join('_');
  }

  /// Validate data before database operations
  void _validateUpsertData(Map<String, dynamic> data) {
    // Check for required fields and data types
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      // Skip null values
      if (value == null) continue;
      
      // Validate specific field types
      if (key.endsWith('_id') && value is String) {
        if (!ValidationUtils.isValidUUID(value)) {
          throw ArgumentError('Invalid UUID format for field: $key');
        }
      }
      
      // Validate JSON data size
      if (value is Map<String, dynamic>) {
        if (!ValidationUtils.isValidJsonData(value)) {
          throw ArgumentError('JSON data too large for field: $key');
        }
      }
      
      // Sanitize string inputs
      if (value is String && !key.endsWith('_id')) {
        data[key] = ValidationUtils.sanitizeString(value);
      }
    }
  }

  /// Get current user ID with validation
  String getCurrentUserId() {
    final userId = SupabaseService.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }
    if (!ValidationUtils.isValidUUID(userId)) {
      throw Exception('Invalid user ID format');
    }
    return userId;
  }

  /// Clear cache for this repository
  void clearRepositoryCache(String repositoryPrefix) {
    // This would need to be implemented with a more sophisticated cache system
    // For now, clear all cache
    SupabaseService.clearAllCache();
    debugPrint('🗑️ Cleared cache for repository: $repositoryPrefix');
  }
}
