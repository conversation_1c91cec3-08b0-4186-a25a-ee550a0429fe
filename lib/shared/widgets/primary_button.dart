import 'package:flutter/material.dart';
import '../../core/theme/color_palette.dart';
import '../../core/theme/typography.dart';

class PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final Widget? icon;
  final bool isFullWidth;
  final EdgeInsetsGeometry? padding;

  const PrimaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.isFullWidth = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: AppColorPalette.primaryOrange,
      foregroundColor: AppColorPalette.white,
      elevation: 0,
      shadowColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      padding: padding ?? const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      minimumSize: isFullWidth ? const Size(double.infinity, 52) : null,
      textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
        color: AppColorPalette.white,
      ),
    );

    if (icon != null) {
      return ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        icon: isLoading 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColorPalette.white),
              ),
            )
          : icon!,
        label: Text(
          text,
          style: const TextStyle(
            color: AppColorPalette.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: buttonStyle,
      child: isLoading
        ? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColorPalette.white),
            ),
          )
        : Text(
            text,
            style: const TextStyle(
              color: AppColorPalette.white,
              fontWeight: FontWeight.w600,
            ),
          ),
    );
  }
} 