import 'package:flutter/material.dart';
import '../../core/theme/color_palette.dart';
import '../../core/theme/typography.dart';

class PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final Widget? icon;
  final bool isFullWidth;
  final EdgeInsetsGeometry? padding;

  const PrimaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.isFullWidth = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: AppColorPalette.accentPrimary, // Use navy instead of orange
      foregroundColor: AppColorPalette.colorCreamWhite, // Use cream white
      elevation: 0, // Completely flat
      shadowColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8), // Minimal rounding for flat Tesla look
      ),
      padding: padding ?? const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      minimumSize: isFullWidth ? const Size(double.infinity, 52) : null,
      textStyle: AppTypography.buttonTextStyle(color: AppColorPalette.colorCreamWhite),
    );

    if (icon != null) {
      return ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        icon: isLoading 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColorPalette.colorCreamWhite),
              ),
            )
          : icon!,
        label: Text(
          text,
          style: AppTypography.buttonTextStyle(color: AppColorPalette.colorCreamWhite),
        ),
      );
    }

    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: buttonStyle,
      child: isLoading
        ? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColorPalette.colorCreamWhite),
            ),
          )
        : Text(
            text,
            style: AppTypography.buttonTextStyle(color: AppColorPalette.colorCreamWhite),
          ),
    );
  }
} 