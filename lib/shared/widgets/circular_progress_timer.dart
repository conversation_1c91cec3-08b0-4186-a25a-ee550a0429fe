import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/color_palette.dart';

/// Circular progress timer with tabular figures and customizable styling
class CircularProgressTimer extends StatefulWidget {
  final int totalSeconds;
  final int currentSeconds;
  final VoidCallback? onTimerComplete;
  final VoidCallback? onTimerTick;
  final double size;
  final double strokeWidth;
  final Color? progressColor;
  final Color? backgroundColor;
  final TextStyle? numberStyle;
  final TextStyle? labelStyle;
  final String? label;
  final bool showControls;
  final VoidCallback? onAddTime;
  final VoidCallback? onSubtractTime;
  final int timeAdjustmentStep;

  const CircularProgressTimer({
    super.key,
    required this.totalSeconds,
    required this.currentSeconds,
    this.onTimerComplete,
    this.onTimerTick,
    this.size = 120,
    this.strokeWidth = 8,
    this.progressColor,
    this.backgroundColor,
    this.numberStyle,
    this.labelStyle,
    this.label = 'seconds',
    this.showControls = false,
    this.onAddTime,
    this.onSubtractTime,
    this.timeAdjustmentStep = 15,
  });

  @override
  State<CircularProgressTimer> createState() => _CircularProgressTimerState();
}

class _CircularProgressTimerState extends State<CircularProgressTimer>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start pulsing when timer is low
    if (widget.currentSeconds <= 10 && widget.currentSeconds > 0) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(CircularProgressTimer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Handle pulsing animation based on remaining time
    if (widget.currentSeconds <= 10 && widget.currentSeconds > 0) {
      if (!_pulseController.isAnimating) {
        _pulseController.repeat(reverse: true);
      }
    } else {
      if (_pulseController.isAnimating) {
        _pulseController.stop();
        _pulseController.reset();
      }
    }

    // Trigger completion callback
    if (oldWidget.currentSeconds > 0 && widget.currentSeconds == 0) {
      HapticFeedback.heavyImpact();
      widget.onTimerComplete?.call();
    }

    // Trigger tick callback
    if (oldWidget.currentSeconds != widget.currentSeconds) {
      widget.onTimerTick?.call();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  double get progress {
    if (widget.totalSeconds == 0) return 0.0;
    return (widget.totalSeconds - widget.currentSeconds) / widget.totalSeconds;
  }

  Color get effectiveProgressColor {
    if (widget.currentSeconds <= 10) {
      return AppColorPalette.error;
    }
    return widget.progressColor ?? AppColorPalette.primaryOrange;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = widget.backgroundColor ?? 
        Colors.white.withOpacity(0.2);

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Timer controls
              if (widget.showControls) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildControlButton(
                      icon: Icons.remove,
                      onPressed: widget.onSubtractTime,
                      label: '-${widget.timeAdjustmentStep}s',
                    ),
                    const SizedBox(width: 40),
                    _buildControlButton(
                      icon: Icons.add,
                      onPressed: widget.onAddTime,
                      label: '+${widget.timeAdjustmentStep}s',
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],

              // Circular timer
              SizedBox(
                width: widget.size,
                height: widget.size,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Background circle
                    SizedBox(
                      width: widget.size,
                      height: widget.size,
                      child: CircularProgressIndicator(
                        value: 1.0,
                        strokeWidth: widget.strokeWidth,
                        backgroundColor: effectiveBackgroundColor,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          effectiveBackgroundColor,
                        ),
                      ),
                    ),
                    
                    // Progress circle
                    SizedBox(
                      width: widget.size,
                      height: widget.size,
                      child: CircularProgressIndicator(
                        value: progress,
                        strokeWidth: widget.strokeWidth,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          effectiveProgressColor,
                        ),
                        strokeCap: StrokeCap.round,
                      ),
                    ),
                    
                    // Timer text
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${widget.currentSeconds}',
                          style: widget.numberStyle ??
                              theme.textTheme.headlineMedium?.copyWith(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
        
                              ),
                        ),
                        if (widget.label != null)
                          Text(
                            widget.label!,
                            style: widget.labelStyle ??
                                theme.textTheme.bodySmall?.copyWith(
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.7),
                                ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String label,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withOpacity(0.1),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                HapticFeedback.lightImpact();
                onPressed?.call();
              },
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

/// Animated countdown timer with spring animation
class AnimatedCountdownTimer extends StatefulWidget {
  final int seconds;
  final VoidCallback? onComplete;
  final double size;
  final TextStyle? textStyle;

  const AnimatedCountdownTimer({
    super.key,
    required this.seconds,
    this.onComplete,
    this.size = 200,
    this.textStyle,
  });

  @override
  State<AnimatedCountdownTimer> createState() => _AnimatedCountdownTimerState();
}

class _AnimatedCountdownTimerState extends State<AnimatedCountdownTimer>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  
  int _currentSecond = 0;

  @override
  void initState() {
    super.initState();
    _currentSecond = widget.seconds;
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _startCountdown();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _startCountdown() async {
    for (int i = _currentSecond; i >= 0; i--) {
      if (!mounted) return;
      
      setState(() => _currentSecond = i);
      
      if (i > 0) {
        _scaleController.reset();
        _fadeController.reset();
        
        await _scaleController.forward();
        await Future.delayed(const Duration(milliseconds: 400));
        await _fadeController.forward();
        
        HapticFeedback.lightImpact();
      } else {
        // Final animation for completion
        HapticFeedback.heavyImpact();
        widget.onComplete?.call();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: Listenable.merge([_scaleController, _fadeController]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: AppColorPalette.primaryGradient,
                  boxShadow: [
                    BoxShadow(
                      color: AppColorPalette.accentPrimary.withOpacity(0.4),
                      blurRadius: 30,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    _currentSecond == 0 ? 'GO!' : '$_currentSecond',
                    style: widget.textStyle ??
                        theme.textTheme.displayLarge?.copyWith(
                          fontSize: 48,
                          fontWeight: FontWeight.bold,
                          color: AppColorPalette.colorCreamWhite,
                        ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
