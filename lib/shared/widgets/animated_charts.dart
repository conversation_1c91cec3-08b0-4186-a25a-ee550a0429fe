import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../core/theme/color_palette.dart';
import '../../core/animations/animation_utils.dart';
import '../../core/animations/counting_animation.dart';

/// Animated circular progress ring with rounded end caps
class AnimatedCircularProgress extends StatefulWidget {
  final double progress;
  final double size;
  final double strokeWidth;
  final Color? progressColor;
  final Color? backgroundColor;
  final Widget? center;
  final Duration animationDuration;
  final bool showGradient;
  final List<Color>? gradientColors;

  const AnimatedCircularProgress({
    super.key,
    required this.progress,
    this.size = 120.0,
    this.strokeWidth = 8.0,
    this.progressColor,
    this.backgroundColor,
    this.center,
    this.animationDuration = const Duration(milliseconds: 1500),
    this.showGradient = true,
    this.gradientColors,
  });

  @override
  State<AnimatedCircularProgress> createState() => _AnimatedCircularProgressState();
}

class _AnimatedCircularProgressState extends State<AnimatedCircularProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.dataVisualization,
      ),
    );

    _controller.forward();
  }

  @override
  void didUpdateWidget(AnimatedCircularProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.progress,
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: FitnessAnimationCurves.dataVisualization,
        ),
      );
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progressColor = widget.progressColor ?? theme.colorScheme.primary;
    final backgroundColor = widget.backgroundColor ?? 
        theme.colorScheme.surfaceContainerHighest.withOpacity(0.3);

    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            alignment: Alignment.center,
            children: [
              CustomPaint(
                size: Size(widget.size, widget.size),
                painter: CircularProgressPainter(
                  progress: _progressAnimation.value,
                  strokeWidth: widget.strokeWidth,
                  progressColor: progressColor,
                  backgroundColor: backgroundColor,
                  showGradient: widget.showGradient,
                  gradientColors: widget.gradientColors ?? [
                    progressColor,
                    progressColor.withOpacity(0.6),
                  ],
                ),
              ),
              if (widget.center != null) widget.center!,
            ],
          ),
        );
      },
    );
  }
}

/// Custom painter for circular progress with gradient support
class CircularProgressPainter extends CustomPainter {
  final double progress;
  final double strokeWidth;
  final Color progressColor;
  final Color backgroundColor;
  final bool showGradient;
  final List<Color> gradientColors;

  CircularProgressPainter({
    required this.progress,
    required this.strokeWidth,
    required this.progressColor,
    required this.backgroundColor,
    required this.showGradient,
    required this.gradientColors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress arc
    final progressPaint = Paint()
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    if (showGradient && gradientColors.length >= 2) {
      progressPaint.shader = SweepGradient(
        colors: gradientColors,
        startAngle: -math.pi / 2,
        endAngle: -math.pi / 2 + (2 * math.pi * progress),
      ).createShader(Rect.fromCircle(center: center, radius: radius));
    } else {
      progressPaint.color = progressColor;
    }

    final sweepAngle = 2 * math.pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.progressColor != progressColor ||
        oldDelegate.backgroundColor != backgroundColor;
  }
}

/// Animated bar chart with spring physics
class AnimatedBarChart extends StatefulWidget {
  final List<ChartData> data;
  final double height;
  final Duration animationDuration;
  final Color? barColor;
  final bool showLabels;
  final bool showValues;

  const AnimatedBarChart({
    super.key,
    required this.data,
    this.height = 200.0,
    this.animationDuration = const Duration(milliseconds: 1200),
    this.barColor,
    this.showLabels = true,
    this.showValues = true,
  });

  @override
  State<AnimatedBarChart> createState() => _AnimatedBarChartState();
}

class _AnimatedBarChartState extends State<AnimatedBarChart>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _controllers = List.generate(
      widget.data.length,
      (index) => AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: controller,
          curve: FitnessAnimationCurves.energyBurst,
        ),
      );
    }).toList();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(
        Duration(milliseconds: i * 100),
        () => _controllers[i].forward(),
      );
    }
  }

  @override
  void dispose() {
    AnimationUtils.disposeControllers(_controllers);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final maxValue = widget.data.map((e) => e.value).reduce(math.max);

    return SizedBox(
      height: widget.height,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: widget.data.asMap().entries.map((entry) {
          final index = entry.key;
          final data = entry.value;
          final barHeight = (data.value / maxValue) * (widget.height - 40);

          return Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate available space for the bar
                  double availableHeight = constraints.maxHeight;
                  double reservedHeight = 0;

                  if (widget.showValues) {
                    reservedHeight += 20; // Approximate height for value text
                  }
                  if (widget.showLabels) {
                    reservedHeight += 20; // Approximate height for label text
                  }
                  reservedHeight += 16; // SizedBox spacing

                  final maxBarHeight = (availableHeight - reservedHeight).clamp(0.0, double.infinity);
                  final actualBarHeight = (barHeight * _animations[index].value).clamp(0.0, maxBarHeight);

                  return Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.showValues)
                        Flexible(
                          child: AnimatedBuilder(
                            animation: _animations[index],
                            builder: (context, child) {
                              return CountingAnimation(
                                value: data.value * _animations[index].value,
                                textStyle: theme.textTheme.labelSmall?.copyWith(fontSize: 10),
                              );
                            },
                          ),
                        ),
                      if (widget.showValues) const SizedBox(height: 4),
                      Flexible(
                        flex: 3,
                        child: AnimatedBuilder(
                          animation: _animations[index],
                          builder: (context, child) {
                            return Container(
                              height: actualBarHeight,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                  colors: [
                                    widget.barColor ?? theme.colorScheme.primary,
                                    (widget.barColor ?? theme.colorScheme.primary)
                                        .withOpacity(0.6),
                                  ],
                                ),
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(4),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      if (widget.showLabels) ...[
                        const SizedBox(height: 4),
                        Flexible(
                          child: Text(
                            data.label,
                            style: theme.textTheme.labelSmall?.copyWith(fontSize: 10),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Data model for charts
class ChartData {
  final String label;
  final double value;
  final Color? color;

  const ChartData({
    required this.label,
    required this.value,
    this.color,
  });
}

/// Animated line chart with smooth curves
class AnimatedLineChart extends StatefulWidget {
  final List<ChartData> data;
  final double height;
  final Duration animationDuration;
  final Color? lineColor;
  final double strokeWidth;
  final bool showDots;
  final bool showGradient;

  const AnimatedLineChart({
    super.key,
    required this.data,
    this.height = 200.0,
    this.animationDuration = const Duration(milliseconds: 2000),
    this.lineColor,
    this.strokeWidth = 3.0,
    this.showDots = true,
    this.showGradient = true,
  });

  @override
  State<AnimatedLineChart> createState() => _AnimatedLineChartState();
}

class _AnimatedLineChartState extends State<AnimatedLineChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pathAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _pathAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.dataVisualization,
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final lineColor = widget.lineColor ?? theme.colorScheme.primary;

    return AnimatedBuilder(
      animation: _pathAnimation,
      builder: (context, child) {
        return SizedBox(
          height: widget.height,
          child: CustomPaint(
            size: Size.infinite,
            painter: LineChartPainter(
              data: widget.data,
              progress: _pathAnimation.value,
              lineColor: lineColor,
              strokeWidth: widget.strokeWidth,
              showDots: widget.showDots,
              showGradient: widget.showGradient,
            ),
          ),
        );
      },
    );
  }
}

/// Custom painter for line chart
class LineChartPainter extends CustomPainter {
  final List<ChartData> data;
  final double progress;
  final Color lineColor;
  final double strokeWidth;
  final bool showDots;
  final bool showGradient;

  LineChartPainter({
    required this.data,
    required this.progress,
    required this.lineColor,
    required this.strokeWidth,
    required this.showDots,
    required this.showGradient,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final maxValue = data.map((e) => e.value).reduce(math.max);
    final minValue = data.map((e) => e.value).reduce(math.min);
    final valueRange = maxValue - minValue;

    final path = Path();
    final points = <Offset>[];

    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final normalizedValue = (data[i].value - minValue) / valueRange;
      final y = size.height - (normalizedValue * size.height);
      
      points.add(Offset(x, y));
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    // Create animated path
    final pathMetrics = path.computeMetrics();
    final animatedPath = Path();
    
    for (final metric in pathMetrics) {
      final extractedPath = metric.extractPath(
        0.0,
        metric.length * progress,
      );
      animatedPath.addPath(extractedPath, Offset.zero);
    }

    // Draw gradient fill
    if (showGradient) {
      final fillPath = Path.from(animatedPath);
      if (points.isNotEmpty) {
        fillPath.lineTo(size.width * progress, size.height);
        fillPath.lineTo(0, size.height);
        fillPath.close();
      }

      final gradientPaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            lineColor.withOpacity(0.3),
            lineColor.withOpacity(0.0),
          ],
        ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

      canvas.drawPath(fillPath, gradientPaint);
    }

    // Draw line
    final linePaint = Paint()
      ..color = lineColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    canvas.drawPath(animatedPath, linePaint);

    // Draw dots
    if (showDots) {
      final dotPaint = Paint()
        ..color = lineColor
        ..style = PaintingStyle.fill;

      final visiblePoints = (points.length * progress).round();
      for (int i = 0; i < visiblePoints; i++) {
        canvas.drawCircle(points[i], 4, dotPaint);
        canvas.drawCircle(
          points[i],
          4,
          Paint()
            ..color = Colors.white
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2,
        );
      }
    }
  }

  @override
  bool shouldRepaint(LineChartPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.data != data ||
        oldDelegate.lineColor != lineColor;
  }
}
