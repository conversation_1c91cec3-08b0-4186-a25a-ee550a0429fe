import 'package:flutter/material.dart';
import 'package:glossy/glossy.dart';
import '../../core/theme/color_palette.dart';
import '../../core/theme/spacing.dart';

/// Enhanced Glass Morphism Card using the Glossy package
/// Provides modern glass effects with navy theme integration
class EnhancedGlassMorphismCard extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final VoidCallback? onTap;
  final double opacity;
  final double strengthX;
  final double strengthY;
  final Color? backgroundColor;
  final List<BoxShadow>? boxShadow;
  final Border? border;

  const EnhancedGlassMorphismCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = 20,
    this.onTap,
    this.opacity = 0.3,
    this.strengthX = 20,
    this.strengthY = 20,
    this.backgroundColor,
    this.boxShadow,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    final card = Container(
      margin: margin,
      child: GlossyContainer(
        width: width ?? double.infinity,
        height: height ?? 200,
        borderRadius: BorderRadius.circular(borderRadius),
        strengthX: strengthX,
        strengthY: strengthY,
        opacity: opacity,
        color: backgroundColor ?? AppColorPalette.colorNavySurface,
        border: border ?? Border.all(
          color: AppColorPalette.glassBorder,
          width: 1,
        ),
        boxShadow: boxShadow ?? [
          BoxShadow(
            color: AppColorPalette.colorBlackPure.withValues(alpha: 0.2),
            blurRadius: 32,
            offset: const Offset(0, 8),
          ),
        ],
        child: Container(
          padding: padding ?? AppSpacing.paddingMd,
          child: child,
        ),
      ),
    );

    if (onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: onTap,
          child: card,
        ),
      );
    }

    return card;
  }
}

/// Workout Card with enhanced glass morphism
class GlossyWorkoutCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const GlossyWorkoutCard({
    super.key,
    required this.title,
    required this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedGlassMorphismCard(
      width: width,
      height: height ?? 120,
      onTap: onTap,
      opacity: 0.25,
      strengthX: 25,
      strengthY: 25,
      backgroundColor: AppColorPalette.colorNavyElevated,
      border: Border.all(
        color: AppColorPalette.glassBorder,
        width: 1.5,
      ),
      boxShadow: [
        BoxShadow(
          color: AppColorPalette.colorNavyBright.withValues(alpha: 0.15),
          blurRadius: 40,
          offset: const Offset(0, 12),
        ),
      ],
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: 16),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColorPalette.textCreamPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColorPalette.textCreamSecondary,
                  ),
                ),
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: 16),
            trailing!,
          ],
        ],
      ),
    );
  }
}

/// Stats Card with glossy effect
class GlossyStatsCard extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final Color? iconColor;
  final VoidCallback? onTap;

  const GlossyStatsCard({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
    this.iconColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedGlassMorphismCard(
      height: 100,
      onTap: onTap,
      opacity: 0.35,
      strengthX: 15,
      strengthY: 15,
      borderRadius: 16,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 24,
            color: iconColor ?? AppColorPalette.colorNavyBright,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColorPalette.textCreamPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColorPalette.textCreamSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Button with glossy effect
class GlossyButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final double? width;
  final double height;
  final Color? backgroundColor;
  final Color? textColor;
  final double borderRadius;
  final Widget? icon;

  const GlossyButton({
    super.key,
    required this.text,
    this.onPressed,
    this.width,
    this.height = 56,
    this.backgroundColor,
    this.textColor,
    this.borderRadius = 16,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      child: GlossyContainer(
        width: width ?? double.infinity,
        height: height,
        borderRadius: BorderRadius.circular(borderRadius),
        strengthX: 15,
        strengthY: 15,
        opacity: 0.4,
        color: backgroundColor ?? AppColorPalette.colorNavyBright,
        border: Border.all(
          color: AppColorPalette.glassBorder,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: (backgroundColor ?? AppColorPalette.colorNavyBright).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(borderRadius),
            onTap: onPressed,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null) ...[
                    icon!,
                    const SizedBox(width: 8),
                  ],
                  Text(
                    text,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color: textColor ?? AppColorPalette.colorCreamWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
