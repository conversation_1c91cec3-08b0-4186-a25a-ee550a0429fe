import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/color_palette.dart';
import '../../core/theme/app_style_manager.dart';
import '../../core/theme/animations.dart';
import '../../core/theme/typography.dart';

/// OpenFit Button System - Exact Implementation
/// Primary Button (Navy gradient) and Energy Button (Orange gradient - workout start only)
/// Primary Button - Default CTA with navy gradient
class PrimaryButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final double? width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final TextStyle? textStyle;
  final bool isLoading;
  final Widget? icon;
  final bool enableHapticFeedback;

  const PrimaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.width,
    this.height = 48,
    this.padding,
    this.margin,
    this.borderRadius = 8, // Flat Tesla design
    this.textStyle,
    this.isLoading = false,
    this.icon,
    this.enableHapticFeedback = true,
  });

  @override
  State<PrimaryButton> createState() => _PrimaryButtonState();
}

class _PrimaryButtonState extends State<PrimaryButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AppAnimations.fast,
      vsync: this,
    );

    _scaleAnimation = AppAnimations.createTouchScale(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      setState(() => _isPressed = true);
      _controller.forward();
      if (widget.enableHapticFeedback) {
        HapticFeedback.lightImpact();
      }
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _controller.reverse();
      widget.onPressed?.call();
    }
  }

  void _handleTapCancel() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  gradient: isEnabled
                      ? AppColorPalette.primaryGradient
                      : LinearGradient(
                          colors: AppColorPalette.primaryGradient.colors
                              .map((color) => color.withValues(alpha: 0.5))
                              .toList(),
                          begin: AppColorPalette.primaryGradient.begin,
                          end: AppColorPalette.primaryGradient.end,
                        ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColorPalette.colorNavyBright.withValues(
                        alpha: isEnabled ? 0.15 : 0.05, // Subtle shadow for flat look
                      ),
                      blurRadius: 6, // Reduced blur for crisp appearance
                      offset: const Offset(0, 2), // Closer to surface
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    onTap: isEnabled ? () {} : null, // Handled by GestureDetector
                    child: Container(
                      padding: widget.padding ??
                          const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                      child: Center(
                        child: widget.isLoading
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColorPalette.colorCreamWhite,
                                  ),
                                ),
                              )
                            : Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (widget.icon != null) ...[
                                    widget.icon!,
                                    const SizedBox(width: 8),
                                  ],
                                  Text(
                                    widget.text,
                                    style: widget.textStyle ??
                                        AppTypography.buttonTextStyle(
                                          color: AppColorPalette.colorCreamWhite,
                                        ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Energy Button - ONLY for workout start buttons (Orange gradient)
class EnergyButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final double? width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final TextStyle? textStyle;
  final bool isLoading;
  final Widget? icon;
  final bool enableHapticFeedback;

  const EnergyButton({
    super.key,
    required this.text,
    this.onPressed,
    this.width,
    this.height = 48,
    this.padding,
    this.margin,
    this.borderRadius = 8, // Flat Tesla design
    this.textStyle,
    this.isLoading = false,
    this.icon,
    this.enableHapticFeedback = true,
  });

  @override
  State<EnergyButton> createState() => _EnergyButtonState();
}

class _EnergyButtonState extends State<EnergyButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AppAnimations.fast,
      vsync: this,
    );

    _scaleAnimation = AppAnimations.createTouchScale(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      setState(() => _isPressed = true);
      _controller.forward();
      if (widget.enableHapticFeedback) {
        HapticFeedback.lightImpact();
      }
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _controller.reverse();
      widget.onPressed?.call();
    }
  }

  void _handleTapCancel() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  gradient: isEnabled
                      ? AppColorPalette.energyGradient
                      : LinearGradient(
                          colors: AppColorPalette.energyGradient.colors
                              .map((color) => color.withValues(alpha: 0.5))
                              .toList(),
                          begin: AppColorPalette.energyGradient.begin,
                          end: AppColorPalette.energyGradient.end,
                        ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColorPalette.colorAccentOrange.withValues(
                        alpha: isEnabled ? 0.15 : 0.05, // Subtle shadow for flat look
                      ),
                      blurRadius: 6, // Reduced blur for crisp appearance
                      offset: const Offset(0, 2), // Closer to surface
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    onTap: isEnabled ? () {} : null, // Handled by GestureDetector
                    child: Container(
                      padding: widget.padding ??
                          const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                      child: Center(
                        child: widget.isLoading
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColorPalette.colorCreamWhite,
                                  ),
                                ),
                              )
                            : Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (widget.icon != null) ...[
                                    widget.icon!,
                                    const SizedBox(width: 8),
                                  ],
                                  Text(
                                    widget.text,
                                    style: widget.textStyle ??
                                        AppTypography.buttonTextStyle(
                                          color: AppColorPalette.colorCreamWhite,
                                        ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// ==================== LEGACY SUPPORT ====================

/// Legacy OrangeGradientButton - now maps to EnergyButton
@Deprecated('Use EnergyButton for workout start buttons or PrimaryButton for default CTAs')
class OrangeGradientButton extends EnergyButton {
  const OrangeGradientButton({
    super.key,
    required super.text,
    super.onPressed,
    super.width,
    super.height = 56,
    super.padding,
    super.margin,
    super.borderRadius = 28,
    super.textStyle,
    super.isLoading = false,
    super.icon,
    super.enableHapticFeedback = true,
  });

}

/// Ghost Button - Secondary actions (Transparent with border)
class GhostButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final double? width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final TextStyle? textStyle;
  final Color? borderColor;
  final Color? textColor;

  const GhostButton({
    super.key,
    required this.text,
    this.onPressed,
    this.width,
    this.height = 48,
    this.padding,
    this.margin,
    this.borderRadius = 8, // Flat Tesla design
    this.textStyle,
    this.borderColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBorderColor = borderColor ??
        AppColorPalette.colorCreamWhite.withValues(alpha: 0.3);
    final effectiveTextColor = textColor ??
        AppColorPalette.colorCreamWhite;

    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: effectiveBorderColor,
          width: 1, // Thinner border for flat Tesla look
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: onPressed,
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 22, vertical: 14),
            constraints: const BoxConstraints(minHeight: 48),
            child: Center(
              child: Text(
                text,
                style: textStyle ??
                    AppTypography.buttonTextStyle(color: effectiveTextColor),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// ==================== LEGACY SUPPORT ====================

/// Legacy SecondaryButton - now maps to GhostButton
@Deprecated('Use GhostButton for secondary actions')
class SecondaryButton extends GhostButton {
  const SecondaryButton({
    super.key,
    required super.text,
    super.onPressed,
    super.width,
    super.height = 56,
    super.padding,
    super.margin,
    super.borderRadius = 28,
    super.textStyle,
    super.borderColor,
    super.textColor,
  });
}

/// Floating action button with orange gradient
class OrangeFloatingActionButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final double size;
  final EdgeInsetsGeometry? margin;

  const OrangeFloatingActionButton({
    super.key,
    this.onPressed,
    required this.child,
    this.size = 56,
    this.margin,
  });

  @override
  State<OrangeFloatingActionButton> createState() =>
      _OrangeFloatingActionButtonState();
}

class _OrangeFloatingActionButtonState extends State<OrangeFloatingActionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTap() {
    _controller.forward().then((_) {
      _controller.reverse();
    });
    HapticFeedback.mediumImpact();
    widget.onPressed?.call();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Container(
          margin: widget.margin,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: AppColorPalette.primaryGradient,
                boxShadow: [
                  BoxShadow(
                    color: AppColorPalette.accentPrimary.withValues(alpha: 0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(widget.size / 2),
                  onTap: _handleTap,
                  child: Center(child: widget.child),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
