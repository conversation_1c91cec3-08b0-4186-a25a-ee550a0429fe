import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class OpenAIRealtimeWebRTCService {
  // WebRTC components
  RTCPeerConnection? _peerConnection;
  RTCDataChannel? _dataChannel;
  MediaStream? _localStream;
  
  // Supabase client
  final _supabase = Supabase.instance.client;
  
  // State management
  final _connectionStateController = StreamController<RTCPeerConnectionState>.broadcast();
  final _transcriptController = StreamController<TranscriptEvent>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  
  // Configuration
  static const String _model = 'gpt-4o-realtime-preview-2024-12-17';
  static const String _voice = 'alloy';
  
  // Workout state
  Map<String, dynamic>? _currentWorkout;
  int _currentExerciseIndex = 0;
  int _currentSetIndex = 0;
  bool _isResting = false;
  int _restTimeRemaining = 0;
  Timer? _restTimer;
  bool _isPaused = false;
  DateTime? _pausedAt;
  Duration _totalPausedDuration = Duration.zero;
  Map<String, dynamic>? _pendingFunctionCall;
  bool _isProcessingResponse = false;
  
  final _workoutStateController = StreamController<Map<String, dynamic>>.broadcast();
  Stream<Map<String, dynamic>> get workoutState => _workoutStateController.stream;
  
  // Streams
  Stream<RTCPeerConnectionState> get connectionState => _connectionStateController.stream;
  Stream<TranscriptEvent> get transcripts => _transcriptController.stream;
  Stream<String> get errors => _errorController.stream;
  
  // Connection status
  bool get isConnected => _peerConnection?.connectionState == RTCPeerConnectionState.RTCPeerConnectionStateConnected;
  
  /// Initialize and connect to OpenAI Realtime API via WebRTC
  Future<void> connect() async {
    try {
      // Get ephemeral token (for now using direct API key for testing)
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('OpenAI API key not found. Please add OPENAI_API_KEY to your .env file');
      }
      
      debugPrint('🔌 Connecting to OpenAI Realtime API...');
      
      // Create peer connection
      final configuration = {
        'iceServers': [
          {'urls': 'stun:stun.l.google.com:19302'},
        ]
      };
      
      _peerConnection = await createPeerConnection(configuration);
      
      // Set up connection state monitoring
      _peerConnection!.onConnectionState = (RTCPeerConnectionState state) {
        debugPrint('🔌 Connection state: $state');
        _connectionStateController.add(state);
        
        if (state == RTCPeerConnectionState.RTCPeerConnectionStateFailed ||
            state == RTCPeerConnectionState.RTCPeerConnectionStateClosed) {
          _errorController.add('Connection lost: $state');
        }
      };
      
      // Set up ICE connection state monitoring
      _peerConnection!.onIceConnectionState = (RTCIceConnectionState state) {
        debugPrint('🧊 ICE connection state: $state');
      };
      
      // Set up to receive remote audio
      _peerConnection!.onAddStream = (MediaStream stream) {
        debugPrint('🎵 Received remote stream with ${stream.getAudioTracks().length} audio tracks');
        stream.getAudioTracks().forEach((track) {
          track.enabled = true;
          debugPrint('🔊 Audio track enabled: ${track.id}');
        });
      };
      
      _peerConnection!.onTrack = (RTCTrackEvent event) {
        debugPrint('🎵 Received remote track: ${event.track.kind}');
        if (event.track.kind == 'audio') {
          // Ensure audio track is enabled
          event.track.enabled = true;
          debugPrint('🔊 Audio track enabled and ready for playback');
          
          if (event.streams.isNotEmpty) {
            final stream = event.streams.first;
            debugPrint('📻 Audio stream ID: ${stream.id}');
          }
        }
      };
      
      // Get user media (microphone)
      final mediaConstraints = {
        'audio': {
          'echoCancellation': true,
          'noiseSuppression': true,
          'autoGainControl': true,
        },
        'video': false
      };
      
      _localStream = await navigator.mediaDevices.getUserMedia(mediaConstraints);
      
      // Add local audio track to peer connection
      final audioTrack = _localStream!.getAudioTracks().first;
      await _peerConnection!.addTrack(audioTrack, _localStream!);
      
      // Create data channel for events
      final dataChannelInit = RTCDataChannelInit()
        ..ordered = true;
      
      _dataChannel = await _peerConnection!.createDataChannel('oai-events', dataChannelInit);
      
      // Set up data channel event handlers
      _dataChannel!.onMessage = (RTCDataChannelMessage message) {
        _handleDataChannelMessage(message.text);
      };
      
      _dataChannel!.onDataChannelState = (RTCDataChannelState state) {
        debugPrint('📊 Data channel state: $state');
        if (state == RTCDataChannelState.RTCDataChannelOpen) {
          debugPrint('✅ Data channel is open and ready');
        }
      };
      
      // Create and set local description (offer)
      debugPrint('📤 Creating SDP offer...');
      final offer = await _peerConnection!.createOffer();
      await _peerConnection!.setLocalDescription(offer);
      
      debugPrint('📡 Sending offer to OpenAI...');
      // Send offer to OpenAI and get answer
      final response = await _sendOfferToOpenAI(offer.sdp!, apiKey);
      
      // Set remote description (answer)
      debugPrint('📥 Setting remote description...');
      final answer = RTCSessionDescription(response, 'answer');
      await _peerConnection!.setRemoteDescription(answer);
      
      debugPrint('✅ WebRTC connection established');
      
    } catch (e) {
      debugPrint('Connection error: $e');
      _errorController.add(e.toString());
      await disconnect();
      rethrow;
    }
  }
  
  /// Send SDP offer to OpenAI and get answer
  Future<String> _sendOfferToOpenAI(String sdp, String apiKey) async {
    final url = Uri.parse('https://api.openai.com/v1/realtime?model=$_model');
    
    // Use HttpClient for precise header control
    final httpClient = HttpClient();
    try {
      final request = await httpClient.postUrl(url);
      
      // Set headers exactly as needed
      request.headers.set('Authorization', 'Bearer $apiKey');
      request.headers.set('Content-Type', 'application/sdp');
      
      // Write the body
      request.write(sdp);
      
      // Send the request
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint('✅ Received SDP answer from OpenAI');
        return responseBody;
      } else {
        debugPrint('❌ Failed to get SDP answer: ${response.statusCode}');
        debugPrint('Response body: $responseBody');
        throw Exception('Failed to get SDP answer: ${response.statusCode} $responseBody');
      }
    } finally {
      httpClient.close();
    }
  }
  
  /// Handle incoming messages from data channel
  void _handleDataChannelMessage(String message) {
    try {
      final event = jsonDecode(message);
      final type = event['type'] as String;
      
      // Only log important events to reduce noise
      const silentEvents = [
        'response.audio_transcript.delta',
        'response.audio.delta',
        'response.output_item.added',
        'response.content_part.added',
        'rate_limits.updated',
        'input_audio_buffer.speech_started',
        'input_audio_buffer.speech_stopped',
        'input_audio_buffer.committed',
        'conversation.item.input_audio_transcription.started',
      ];
      
      if (!silentEvents.contains(type)) {
        debugPrint('📡 Event: $type');
      }
      
      switch (type) {
        case 'session.created':
          _handleSessionCreated(event);
          break;
          
        case 'conversation.item.created':
          _handleConversationItemCreated(event);
          break;
          
        case 'response.audio_transcript.delta':
          _handleTranscriptDelta(event, isUser: false);
          break;
          
        case 'conversation.item.input_audio_transcription.completed':
          _handleInputTranscriptionCompleted(event);
          break;
          
        case 'response.done':
          _handleResponseDone(event);
          break;
          
        case 'response.function_call_arguments.done':
          _handleFunctionCallArgumentsDone(event);
          break;
          
        case 'response.audio.done':
          debugPrint('🔊 Audio response completed');
          break;
          
        case 'error':
          _handleError(event);
          break;
          
        default:
          // Silently ignore other events
          break;
      }
    } catch (e) {
      debugPrint('❌ Error handling message: $e');
      _errorController.add('Message handling error: $e');
    }
  }
  
  /// Handle session created event
  void _handleSessionCreated(Map<String, dynamic> event) {
    debugPrint('✅ Session created');
    
    // Build context with actual workout data if available
    String workoutContext = '';
    if (_currentWorkout != null) {
      final exercises = _currentWorkout!['exercises'] as List;
      workoutContext = '''
        
        CURRENT WORKOUT: ${_currentWorkout!['name']}
        Total exercises: ${exercises.length}
        
        Exercise list:
        ''';
      for (int i = 0; i < exercises.length; i++) {
        final ex = exercises[i];
        workoutContext += '''
        ${i + 1}. ${ex['name']} - ${ex['sets']} sets of ${ex['reps'][0]} reps at ${ex['weight'][0]} lbs
        ''';
      }
      
      if (_currentExerciseIndex < exercises.length) {
        workoutContext += '''
        
        Current position: Exercise ${_currentExerciseIndex + 1} (${exercises[_currentExerciseIndex]['name']}), Set ${_currentSetIndex + 1} of ${exercises[_currentExerciseIndex]['sets']}
        ''';
      } else {
        workoutContext += '''
        
        Workout completed! All ${exercises.length} exercises finished.
        ''';
      }
    }
    
    // Update session with our configuration
    final updateEvent = {
      'type': 'session.update',
      'session': {
        'modalities': ['text', 'audio'],
        'voice': _voice,
        'instructions': '''You are an AI fitness coach helping with hands-free workout tracking.
        Be encouraging, knowledgeable, and supportive. Keep responses concise and natural.
        $workoutContext
        
        IMPORTANT WORKOUT PROGRESSION RULES:
        - Each exercise has multiple sets (usually 3)
        - Users must complete ALL sets of an exercise before moving to the next exercise
        - After logging a set, if there are more sets remaining for the current exercise, prepare for the next set of the SAME exercise
        - Only move to the next exercise when ALL sets of the current exercise are complete
        - Always use the actual exercise names and data from the loaded workout
        - NEVER make up exercise names or data - use only what's in the current workout
        - NEVER add exercises that aren't part of the original workout plan
        - When the workout is complete (all exercises done), do NOT add more exercises
        
        You can help users track their workouts with these commands:
        - "Start workout" or "Load today's workout" - loads the current workout
        - "Log set: X reps at Y pounds" - records a completed set
        - "Next exercise" - moves to the next exercise (only use when all sets are done)
        - "Start rest" - begins rest timer
        - "Skip rest" - skips the rest period
        - "What's next?" or "Current exercise" - tells current exercise and set number
        - "Complete workout" - marks workout as finished
        - "Previous exercise" - go back to previous exercise
        - "Repeat exercise instructions" - hear the exercise instructions again
        - "How many sets left?" - get remaining sets for current exercise
        - "Pause workout" - temporarily pause the workout
        - "Resume workout" - continue after pausing
        
        When logging sets, be flexible with phrasing:
        - "I did 10 reps with 50 pounds"
        - "Completed 8 reps at 45"
        - "12 reps, 60 pounds"
        - "Set done: 15 reps, 35 pounds"
        - Just saying "8 reps" (will use 0 weight if not specified)
        
        After each logged set, ALWAYS mention:
        1. Confirmation with set number ("Great job on set 1!")
        2. What's next - if more sets remain: "2 sets left for [exercise name]"
        3. If last set of exercise: "All sets done for [exercise]! Ready for [next exercise]?"
        
        When starting rest, count down the last 10 seconds.
        Keep responses under 3 sentences. Be motivating but not overly enthusiastic.
        
        CRITICAL RULES:
        - This workout has a fixed number of exercises. Do NOT suggest or add any exercises beyond the original plan.
        - When all exercises are complete, congratulate the user and mark the workout as finished.
        - For replace_exercise, only replace with exercises from the database, never invent new ones.
        - Always stay within the bounds of the current workout plan.''',
        'input_audio_transcription': {
          'model': 'whisper-1',
        },
        'turn_detection': {
          'type': 'server_vad',
          'threshold': 0.5,
          'prefix_padding_ms': 300,
          'silence_duration_ms': 500,
        },
        'output_audio_format': 'pcm16',
        'tools': [
          {
            'type': 'function',
            'name': 'load_workout',
            'description': 'Load today\'s workout or a specific workout',
            'parameters': {
              'type': 'object',
              'properties': {
                'workout_id': {
                  'type': 'string',
                  'description': 'Optional workout ID. If not provided, loads today\'s workout',
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'log_set',
            'description': 'Log a completed set with reps and weight',
            'parameters': {
              'type': 'object',
              'properties': {
                'reps': {
                  'type': 'integer',
                  'description': 'Number of reps completed',
                },
                'weight': {
                  'type': 'number',
                  'description': 'Weight used in pounds',
                },
              },
              'required': ['reps', 'weight'],
            },
          },
          {
            'type': 'function',
            'name': 'next_exercise',
            'description': 'Move to the next exercise in the workout',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'start_rest',
            'description': 'Start the rest timer',
            'parameters': {
              'type': 'object',
              'properties': {
                'duration': {
                  'type': 'integer',
                  'description': 'Rest duration in seconds (optional, uses exercise default if not provided)',
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'skip_rest',
            'description': 'Skip the current rest period',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'get_current_exercise',
            'description': 'Get information about the current exercise and set',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'complete_workout',
            'description': 'Mark the workout as completed',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'previous_exercise',
            'description': 'Go back to the previous exercise',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'get_exercise_instructions',
            'description': 'Get detailed instructions for the current exercise',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'pause_workout',
            'description': 'Pause the current workout',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'resume_workout',
            'description': 'Resume a paused workout',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'modify_weight',
            'description': 'Modify the target weight for upcoming sets',
            'parameters': {
              'type': 'object',
              'properties': {
                'weight': {
                  'type': 'number',
                  'description': 'New weight in pounds',
                },
              },
              'required': ['weight'],
            },
          },
          {
            'type': 'function',
            'name': 'skip_exercise',
            'description': 'Skip the current exercise and move to the next one',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'add_rest_time',
            'description': 'Add more time to the current rest period',
            'parameters': {
              'type': 'object',
              'properties': {
                'seconds': {
                  'type': 'integer',
                  'description': 'Additional seconds to add to rest (default 30)',
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'replace_exercise',
            'description': 'Replace the current exercise with a different one',
            'parameters': {
              'type': 'object',
              'properties': {
                'new_exercise_name': {
                  'type': 'string',
                  'description': 'Name of the exercise to replace with',
                },
                'muscle_group': {
                  'type': 'string',
                  'description': 'Optional muscle group to filter exercises (e.g., "back", "chest", "legs")',
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'search_exercises',
            'description': 'Search for available exercises in the database',
            'parameters': {
              'type': 'object',
              'properties': {
                'muscle_group': {
                  'type': 'string',
                  'description': 'Filter by muscle group (e.g., "back", "chest", "legs")',
                },
                'equipment': {
                  'type': 'string',
                  'description': 'Filter by equipment (e.g., "dumbbell", "barbell", "bodyweight")',
                },
              },
              'required': [],
            },
          },
        ],
        'tool_choice': 'auto',
      },
    };
    
    _sendEvent(updateEvent);
  }
  
  /// Handle conversation item created
  void _handleConversationItemCreated(Map<String, dynamic> event) {
    final item = event['item'];
    debugPrint('Conversation item created: ${item['type']}');
  }
  
  /// Handle transcript delta
  void _handleTranscriptDelta(Map<String, dynamic> event, {required bool isUser}) {
    final delta = event['delta'] as String;
    _transcriptController.add(TranscriptEvent(
      text: delta,
      isUser: isUser,
      isDelta: true,
    ));
  }
  
  /// Handle input transcription completed
  void _handleInputTranscriptionCompleted(Map<String, dynamic> event) {
    final transcript = event['transcript'] as String;
    _transcriptController.add(TranscriptEvent(
      text: transcript,
      isUser: true,
      isDelta: false,
      isComplete: true,
    ));
  }
  
  /// Handle response done
  void _handleResponseDone(Map<String, dynamic> event) {
    debugPrint('Response completed');
    final response = event['response'];
    
    // Reset processing flag
    _isProcessingResponse = false;
    
    // Check if we have a pending function call from function_call_arguments.done
    if (_pendingFunctionCall != null) {
      final functionCall = _pendingFunctionCall!;
      _pendingFunctionCall = null; // Clear it
      _handleFunctionCall(functionCall);
      return; // Don't process further
    }
    
    // Check if this is a function call response
    if (response['output'] != null && response['output'].isNotEmpty) {
      final output = response['output'][0];
      
      if (output['type'] == 'function_call') {
        // Handle function call
        _handleFunctionCall(output);
      } else if (output['type'] == 'message' && output['content'] != null) {
        // Handle regular message
        for (final content in output['content']) {
          if (content['type'] == 'audio' && content['transcript'] != null) {
            _transcriptController.add(TranscriptEvent(
              text: content['transcript'],
              isUser: false,
              isDelta: false,
              isComplete: true,
            ));
          }
        }
      }
    }
  }
  
  /// Handle function call arguments done
  void _handleFunctionCallArgumentsDone(Map<String, dynamic> event) {
    debugPrint('Function call arguments done: ${event}');
    
    // Store the function call data for when response.done is received
    // We'll handle it there to avoid duplicate responses
    _pendingFunctionCall = {
      'name': event['name'],
      'call_id': event['call_id'],
      'arguments': event['arguments'],
    };
  }
  
  /// Handle function call
  void _handleFunctionCall(Map<String, dynamic> functionCall) async {
    final name = functionCall['name'];
    final callId = functionCall['call_id'];
    final arguments = jsonDecode(functionCall['arguments'] ?? '{}');
    
    debugPrint('🔧 Function Call: $name');
    if (arguments.isNotEmpty) {
      debugPrint('   Args: $arguments');
    }
    
    dynamic result;
    
    switch (name) {
      case 'load_workout':
        result = await _loadWorkout(arguments['workout_id']);
        break;
        
      case 'log_set':
        result = await _logSet(arguments['reps'] as int, arguments['weight'] as num);
        break;
        
      case 'next_exercise':
        result = _nextExercise();
        break;
        
      case 'start_rest':
        result = _startRest(arguments['duration'] as int?);
        break;
        
      case 'skip_rest':
        result = _skipRest();
        break;
        
      case 'get_current_exercise':
        result = _getCurrentExercise();
        break;
        
      case 'complete_workout':
        result = await _completeWorkout();
        break;
        
      case 'previous_exercise':
        result = _previousExercise();
        break;
        
      case 'get_exercise_instructions':
        result = await _getExerciseInstructions();
        break;
        
      case 'pause_workout':
        result = _pauseWorkout();
        break;
        
      case 'resume_workout':
        result = _resumeWorkout();
        break;
        
      case 'modify_weight':
        result = _modifyWeight(arguments['weight'] as num);
        break;
        
      case 'skip_exercise':
        result = _skipExercise();
        break;
        
      case 'add_rest_time':
        result = _addRestTime(arguments['seconds'] as int?);
        break;
        
      case 'replace_exercise':
        result = await _replaceExercise(
          arguments['new_exercise_name'] as String?,
          arguments['muscle_group'] as String?,
        );
        break;
        
      case 'search_exercises':
        result = await _searchExercises(
          arguments['muscle_group'] as String?,
          arguments['equipment'] as String?,
        );
        break;
        
      default:
        result = {'error': 'Unknown function'};
    }
    
    // Send function result back
    final resultEvent = {
      'type': 'conversation.item.create',
      'item': {
        'type': 'function_call_output',
        'call_id': callId,
        'output': jsonEncode(result),
      },
    };
    
    _sendEvent(resultEvent);
    
    // Trigger a response to continue the conversation
    // Add a small delay to avoid "Conversation already has an active response" error
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!_isProcessingResponse) {
        _isProcessingResponse = true;
        _sendEvent({
          'type': 'response.create',
          'response': {
            'modalities': ['text', 'audio'],
          }
        });
      }
    });
  }
  
  // Workout tracking functions
  Future<Map<String, dynamic>> _loadWorkout(String? workoutId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        return {
          'error': 'User not authenticated',
          'message': 'Please sign in to track your workouts'
        };
      }
      
      // Load today's workout or specific workout
      Map<String, dynamic>? workout;
      
      if (workoutId != null) {
        // Load specific workout
        final response = await _supabase
            .from('workouts')
            .select('*, workout_exercises(*, exercises(*))')
            .eq('id', workoutId)
            .single();
        workout = response;
      } else {
        // Load today's incomplete workout
        final response = await _supabase
            .from('workouts')
            .select('*, workout_exercises(*, exercises(*))')
            .eq('user_id', userId)
            .or('is_completed.is.null,is_completed.eq.false')
            .order('created_at', ascending: false)
            .limit(1);
        
        if (response.isNotEmpty) {
          workout = response.first;
        }
      }
      
      if (workout == null) {
        return {
          'error': 'No workout found',
          'message': 'You don\'t have any active workouts. Please create a workout first.'
        };
      }
      
      // Transform workout data
      final exercises = (workout['workout_exercises'] as List).map((we) {
        final exercise = we['exercises'];
        return {
          'id': we['id'],
          'exercise_id': we['exercise_id'],
          'name': we['name'],
          'sets': we['sets'] ?? 3,
          'reps': we['reps'] ?? [10, 10, 10],
          'weight': we['weight'] ?? [0, 0, 0],
          'rest': we['rest_interval'] ?? 60,
          'primary_muscle': exercise['primary_muscle'] ?? '',
          'equipment': exercise['equipment'] ?? '',
          'order': we['order_index'] ?? 0,
        };
      }).toList();
      
      // Sort by order
      exercises.sort((a, b) => (a['order'] as int).compareTo(b['order'] as int));
      
      _currentWorkout = {
        'id': workout['id'],
        'name': workout['name'],
        'exercises': exercises,
        'started_at': DateTime.now().toIso8601String(),
      };
      
      // Always start fresh from the beginning when loading a workout
      _currentExerciseIndex = 0;
      _currentSetIndex = 0;
      _isPaused = false;
      _pausedAt = null;
      _totalPausedDuration = Duration.zero;
      
      debugPrint('📊 Loaded workout: ${workout['name']} with ${exercises.length} exercises');
      debugPrint('   Starting at: Exercise 1 (${exercises[0]['name']}), Set 1');
      
      // Don't load previously completed sets to avoid state corruption
      // await _loadCompletedSets();
      
      _updateWorkoutState();
      
      if (exercises.isEmpty) {
        return {
          'workout': workout['name'],
          'message': 'No exercises in this workout',
        };
      }
      
      final currentExercise = exercises[_currentExerciseIndex];
      // Update AI context with loaded workout
      _updateAIContext();
      
      return {
        'workout': workout['name'],
        'first_exercise': currentExercise['name'],
        'current_exercise_index': _currentExerciseIndex,
        'current_set': _currentSetIndex + 1,
        'sets': currentExercise['sets'],
        'target_reps': currentExercise['reps'][0],
        'target_weight': currentExercise['weight'][0],
        'total_exercises': exercises.length,
        'message': 'Loaded ${workout['name']}. Starting with ${currentExercise['name']}, set 1 of ${currentExercise['sets']}. Target: ${currentExercise['reps'][0]} reps at ${currentExercise['weight'][0]} pounds.',
      };
    } catch (e) {
      debugPrint('Error loading workout: $e');
      return {'error': 'Failed to load workout: ${e.toString()}'};
    }
  }
  
  Future<void> _loadCompletedSets() async {
    if (_currentWorkout == null) return;
    
    try {
      // Load any completed sets for this workout
      final response = await _supabase
          .from('completed_sets')
          .select('*')
          .eq('workout_id', _currentWorkout!['id'])
          .order('created_at', ascending: false);
      
      if (response.isNotEmpty) {
        // Find the latest exercise and set completed
        for (final set in response) {
          final exercises = _currentWorkout!['exercises'] as List;
          final exerciseIndex = exercises.indexWhere((e) => e['id'] == set['workout_exercise_id']);
          if (exerciseIndex != -1) {
            final setOrder = set['performed_set_order'] ?? 1;
            // Update current position if this is further along
            if (exerciseIndex > _currentExerciseIndex || 
                (exerciseIndex == _currentExerciseIndex && setOrder > _currentSetIndex)) {
              _currentExerciseIndex = exerciseIndex;
              _currentSetIndex = setOrder;
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading completed sets: $e');
    }
  }
  
  Future<Map<String, dynamic>> _logSet(int reps, num weight) async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded', 'message': 'Please start a workout first'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    if (_currentExerciseIndex >= exercises.length) {
      return {'error': 'Workout already completed', 'message': 'Great job finishing your workout!'};
    }
    
    // Validate indices to prevent corruption
    if (_currentExerciseIndex < 0 || _currentExerciseIndex >= exercises.length) {
      debugPrint('⚠️ Invalid exercise index: $_currentExerciseIndex, resetting to 0');
      _currentExerciseIndex = 0;
      _currentSetIndex = 0;
    }
    
    final currentExercise = exercises[_currentExerciseIndex];
    final totalSets = currentExercise['sets'] as int;
    final exerciseId = currentExercise['exercise_id'];
    
    // Validate set index
    if (_currentSetIndex >= totalSets) {
      debugPrint('⚠️ Invalid set index: $_currentSetIndex for exercise with $totalSets sets');
      return {'error': 'All sets completed for this exercise', 'message': 'Please move to the next exercise'};
    }
    
    try {
      // Check for personal record
      bool isPersonalRecord = false;
      if (weight > 0) {
        final prCheck = await _supabase
            .from('completed_sets')
            .select('performed_weight')
            .eq('workout_exercise_id', currentExercise['id'])
            .order('performed_weight', ascending: false)
            .limit(1);
        
        if (prCheck.isEmpty || (prCheck.first['performed_weight'] as num) < weight) {
          isPersonalRecord = true;
        }
      }
      
      // Save to Supabase completed_sets table (matching the schema from workout_session_provider)
      await _supabase.from('completed_sets').insert({
        'workout_id': _currentWorkout!['id'],
        'workout_exercise_id': currentExercise['id'],
        'performed_set_order': _currentSetIndex + 1,
        'performed_reps': reps,
        'performed_weight': weight.round(), // Round weight to match schema
        'set_feedback_difficulty': null, // Optional difficulty feedback
      });
      
      // Track in local state for PR detection
      if (!_currentWorkout!.containsKey('completed_sets')) {
        _currentWorkout!['completed_sets'] = [];
      }
      final completedSetNumber = _currentSetIndex + 1; // Store before incrementing
      
      _currentWorkout!['completed_sets'].add({
        'exercise': currentExercise['name'],
        'exercise_index': _currentExerciseIndex,
        'set': completedSetNumber,
        'reps': reps,
        'weight': weight,
        'timestamp': DateTime.now().toIso8601String(),
        'is_pr': isPersonalRecord,
      });
      
      debugPrint('💾 Saved set to local state: ${currentExercise['name']} - Set $completedSetNumber');
      
      _currentSetIndex++;
      
      final setsRemaining = totalSets - _currentSetIndex;
      final isLastSet = setsRemaining == 0;
      
      debugPrint('📈 Set logged: Exercise ${_currentExerciseIndex + 1}/${exercises.length}, Set $completedSetNumber/$totalSets');
      debugPrint('   Sets remaining for ${currentExercise['name']}: $setsRemaining');
      
      if (_currentSetIndex >= totalSets) {
        // Completed all sets for this exercise
        debugPrint('✅ All sets completed for ${currentExercise['name']}');
        
        if (_currentExerciseIndex < exercises.length - 1) {
          // Move to next exercise only if not the last exercise
          _currentExerciseIndex++;
          _currentSetIndex = 0;
          final nextExercise = exercises[_currentExerciseIndex];
          debugPrint('➡️ Moving to next exercise: ${nextExercise['name']}');
        } else {
          debugPrint('🎉 All exercises completed!');
          // Don't add any new exercises - workout is complete
        }
      }
      
      _updateWorkoutState();
      
      // Build response message with actual exercise names
      String message = isPersonalRecord ? 
        'Personal record! Amazing work!' : 
        'Great job!';
      
      message += ' That was set $completedSetNumber of $totalSets for ${currentExercise['name']}.';
      
      if (setsRemaining > 0) {
        message += ' $setsRemaining ${setsRemaining == 1 ? "set" : "sets"} remaining for ${currentExercise['name']}.';
      } else if (_currentExerciseIndex < exercises.length - 1) {
        final nextExercise = exercises[_currentExerciseIndex];
        message += ' All sets completed! Next up: ${nextExercise['name']}.';
      } else {
        message += ' Congratulations! You\'ve completed the entire workout! All ${exercises.length} exercises done.';
      }
      
      return {
        'logged': true,
        'exercise': currentExercise['name'],
        'set': _currentSetIndex,
        'reps': reps,
        'weight': weight,
        'sets_remaining': setsRemaining,
        'is_last_set': isLastSet,
        'is_personal_record': isPersonalRecord,
        'message': message,
        'rest_duration': currentExercise['rest'] ?? 60,
      };
    } catch (e) {
      debugPrint('Error logging set: $e');
      return {'error': 'Failed to log set: ${e.toString()}'};
    }
  }
  
  Map<String, dynamic> _nextExercise() {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    
    // Check if we're already at or beyond the last exercise
    if (_currentExerciseIndex >= exercises.length - 1) {
      return {
        'completed': true, 
        'message': 'You\'ve already completed all exercises! Great workout!',
        'total_exercises': exercises.length,
      };
    }
    
    _currentExerciseIndex++;
    _currentSetIndex = 0;
    
    final nextExercise = exercises[_currentExerciseIndex];
    _updateWorkoutState();
    
    return {
      'exercise': nextExercise['name'],
      'sets': nextExercise['sets'],
      'target_reps': nextExercise['reps'][0],
      'target_weight': nextExercise['weight'][0],
      'primary_muscle': nextExercise['primary_muscle'],
    };
  }
  
  Map<String, dynamic> _startRest(int? duration) {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    final currentExercise = exercises[_currentExerciseIndex];
    final restDuration = duration ?? currentExercise['rest'] as int;
    
    _isResting = true;
    _restTimeRemaining = restDuration;
    
    _restTimer?.cancel();
    _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _restTimeRemaining--;
      _updateWorkoutState();
      
      if (_restTimeRemaining <= 0) {
        timer.cancel();
        _isResting = false;
        _updateWorkoutState();
      }
    });
    
    return {
      'resting': true,
      'duration': restDuration,
    };
  }
  
  Map<String, dynamic> _skipRest() {
    _restTimer?.cancel();
    _isResting = false;
    _restTimeRemaining = 0;
    _updateWorkoutState();
    
    return {'rest_skipped': true};
  }
  
  Map<String, dynamic> _getCurrentExercise() {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    
    // Validate indices
    if (_currentExerciseIndex < 0 || _currentExerciseIndex >= exercises.length) {
      return {
        'completed': true, 
        'message': 'Great job! You\'ve completed all ${exercises.length} exercises in your workout!',
        'total_exercises_completed': exercises.length,
        'workout_name': _currentWorkout!['name'],
      };
    }
    
    final currentExercise = exercises[_currentExerciseIndex];
    final repsArray = currentExercise['reps'] as List;
    final weightArray = currentExercise['weight'] as List;
    
    // Get target reps/weight with bounds checking
    final targetReps = _currentSetIndex < repsArray.length ? repsArray[_currentSetIndex] : repsArray[0];
    final targetWeight = _currentSetIndex < weightArray.length ? weightArray[_currentSetIndex] : weightArray[0];
    
    return {
      'exercise': currentExercise['name'],
      'exercise_number': _currentExerciseIndex + 1,
      'total_exercises': exercises.length,
      'set': _currentSetIndex + 1,
      'total_sets': currentExercise['sets'],
      'target_reps': targetReps,
      'target_weight': targetWeight,
      'primary_muscle': currentExercise['primary_muscle'],
      'is_resting': _isResting,
      'rest_remaining': _restTimeRemaining,
      'message': 'Currently on ${currentExercise['name']} (exercise ${_currentExerciseIndex + 1} of ${exercises.length}), set ${_currentSetIndex + 1} of ${currentExercise['sets']}. Target: $targetReps reps at $targetWeight pounds.',
    };
  }
  
  Future<Map<String, dynamic>> _completeWorkout() async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    try {
      final workoutId = _currentWorkout!['id'];
      final workoutName = _currentWorkout!['name'];
      final completedSets = _currentWorkout!['completed_sets'] ?? [];
      final exercises = _currentWorkout!['exercises'] as List;
      
      // Calculate workout summary
      final totalSetsCompleted = completedSets.length;
      final exercisesCompleted = _currentExerciseIndex;
      
      // Calculate duration accounting for paused time
      final startedAt = DateTime.parse(_currentWorkout!['started_at']);
      final totalDuration = DateTime.now().difference(startedAt);
      final activeDuration = totalDuration - _totalPausedDuration;
      final duration = activeDuration.inSeconds;
      
      // Create workout summary
      final workoutSummary = {
        'total_sets_completed': totalSetsCompleted,
        'exercises_completed': exercisesCompleted,
        'workout_name': workoutName,
        'completed_at': DateTime.now().toIso8601String(),
        'duration_seconds': duration,
        'session_data': {
          'completed_sets': completedSets,
          'exercises': exercises.map((e) => e['name']).toList(),
        },
      };
      
      // Save to Supabase completed_workouts table
      await _supabase.from('completed_workouts').insert({
        'workout_id': workoutId,
        'user_id': _supabase.auth.currentUser!.id,
        'date_completed': DateTime.now().toIso8601String(),
        'duration': duration,
        'user_feedback_completed_workout': 'Completed via hands-free voice tracking',
        'completed_workout_summary': workoutSummary,
        'calories_burned': 0, // Could calculate based on exercises
        'rating': null, // User can rate later
      });
      
      // Update workout as completed
      await _supabase.from('workouts').update({
        'is_completed': true,
        'end_time': DateTime.now().toIso8601String(),
      }).eq('id', workoutId);
      
      // Clear workout state
      _currentWorkout = null;
      _currentExerciseIndex = 0;
      _currentSetIndex = 0;
      _restTimer?.cancel();
      _isResting = false;
      _isPaused = false;
      _pausedAt = null;
      _totalPausedDuration = Duration.zero;
      _updateWorkoutState();
      
      return {
        'completed': true,
        'workout': workoutName,
        'total_sets': totalSetsCompleted,
        'exercises_completed': exercisesCompleted,
        'duration_minutes': (duration / 60).round(),
        'message': 'Excellent work! Your workout has been saved.',
      };
    } catch (e) {
      debugPrint('Error completing workout: $e');
      return {'error': 'Failed to save workout: ${e.toString()}'};
    }
  }
  
  void _updateWorkoutState() {
    final state = {
      'has_workout': _currentWorkout != null,
      'workout_name': _currentWorkout?['name'],
      'current_exercise_index': _currentExerciseIndex,
      'current_set_index': _currentSetIndex,
      'is_resting': _isResting,
      'rest_time_remaining': _restTimeRemaining,
      'is_paused': _isPaused,
    };
    
    // Add completed sets info if available
    if (_currentWorkout != null && _currentWorkout!.containsKey('completed_sets')) {
      state['completed_sets'] = _currentWorkout!['completed_sets'];
    }
    
    _workoutStateController.add(state);
  }
  
  /// Update AI context with current workout information
  void _updateAIContext() {
    if (_currentWorkout == null || _dataChannel?.state != RTCDataChannelState.RTCDataChannelOpen) {
      return;
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    
    // Build updated context
    String workoutContext = '''
CURRENT WORKOUT: ${_currentWorkout!['name']}
Total exercises: ${exercises.length}

Exercise list:
''';
    
    for (int i = 0; i < exercises.length; i++) {
      final ex = exercises[i];
      workoutContext += '''
${i + 1}. ${ex['name']} - ${ex['sets']} sets of ${ex['reps'][0]} reps at ${ex['weight'][0]} lbs
''';
    }
    
    if (_currentExerciseIndex < exercises.length) {
      workoutContext += '''

Current position: Exercise ${_currentExerciseIndex + 1} (${exercises[_currentExerciseIndex]['name']}), Set ${_currentSetIndex + 1} of ${exercises[_currentExerciseIndex]['sets']}
''';
    } else {
      workoutContext += '''

Workout completed! All ${exercises.length} exercises finished.
''';
    }
    
    // Send context update
    final contextEvent = {
      'type': 'conversation.item.create',
      'item': {
        'type': 'message',
        'role': 'system',
        'content': [
          {
            'type': 'input_text',
            'text': 'Workout context update: $workoutContext',
          }
        ],
      },
    };
    
    _sendEvent(contextEvent);
    debugPrint('🔄 Updated AI context with workout information');
  }
  
  Map<String, dynamic> _previousExercise() {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    if (_currentExerciseIndex > 0) {
      _currentExerciseIndex--;
      _currentSetIndex = 0;
      
      final exercises = _currentWorkout!['exercises'] as List;
      final exercise = exercises[_currentExerciseIndex];
      
      _updateWorkoutState();
      
      return {
        'exercise': exercise['name'],
        'sets': exercise['sets'],
        'target_reps': exercise['reps'][0],
        'target_weight': exercise['weight'][0],
        'message': 'Going back to ${exercise['name']}',
      };
    } else {
      return {'error': 'Already at the first exercise'};
    }
  }
  
  Future<Map<String, dynamic>> _getExerciseInstructions() async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    if (_currentExerciseIndex >= exercises.length) {
      return {'error': 'No current exercise'};
    }
    
    final currentExercise = exercises[_currentExerciseIndex];
    final exerciseId = currentExercise['exercise_id'];
    
    try {
      // Fetch detailed instructions from exercises table
      final response = await _supabase
          .from('exercises')
          .select('instructions, form_tips, common_mistakes')
          .eq('id', exerciseId)
          .single();
      
      return {
        'exercise': currentExercise['name'],
        'instructions': response['instructions'] ?? 'No instructions available',
        'form_tips': response['form_tips'],
        'common_mistakes': response['common_mistakes'],
        'primary_muscle': currentExercise['primary_muscle'],
        'equipment': currentExercise['equipment'],
      };
    } catch (e) {
      return {
        'exercise': currentExercise['name'],
        'instructions': 'Basic exercise instructions not available',
        'primary_muscle': currentExercise['primary_muscle'],
      };
    }
  }
  
  Map<String, dynamic> _pauseWorkout() {
    if (_currentWorkout == null) {
      return {'error': 'No workout active'};
    }
    
    if (_isPaused) {
      return {'error': 'Workout already paused'};
    }
    
    _isPaused = true;
    _pausedAt = DateTime.now();
    
    // Pause rest timer if active
    if (_isResting) {
      _restTimer?.cancel();
    }
    
    _updateWorkoutState();
    
    return {
      'paused': true,
      'message': 'Workout paused. Say "resume workout" when ready to continue.',
    };
  }
  
  Map<String, dynamic> _resumeWorkout() {
    if (!_isPaused) {
      return {'error': 'Workout is not paused'};
    }
    
    if (_pausedAt != null) {
      _totalPausedDuration += DateTime.now().difference(_pausedAt!);
    }
    
    _isPaused = false;
    _pausedAt = null;
    
    // Resume rest timer if was resting
    if (_isResting && _restTimeRemaining > 0) {
      _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        _restTimeRemaining--;
        _updateWorkoutState();
        
        if (_restTimeRemaining <= 0) {
          timer.cancel();
          _isResting = false;
          _updateWorkoutState();
        }
      });
    }
    
    _updateWorkoutState();
    
    return {
      'resumed': true,
      'message': _isResting ? 
        'Workout resumed. Rest timer continuing with $_restTimeRemaining seconds.' :
        'Workout resumed. Let\'s continue!',
    };
  }
  
  Map<String, dynamic> _modifyWeight(num newWeight) {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    if (_currentExerciseIndex >= exercises.length) {
      return {'error': 'No current exercise'};
    }
    
    final currentExercise = exercises[_currentExerciseIndex];
    final weights = currentExercise['weight'] as List;
    
    // Update weight for remaining sets
    for (int i = _currentSetIndex; i < weights.length; i++) {
      weights[i] = newWeight;
    }
    
    _updateWorkoutState();
    
    return {
      'modified': true,
      'new_weight': newWeight,
      'exercise': currentExercise['name'],
      'message': 'Weight updated to $newWeight pounds for remaining sets',
    };
  }
  
  /// Skip current exercise and move to next
  Map<String, dynamic> _skipExercise() {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    
    // Check if we're at or beyond the last exercise
    if (_currentExerciseIndex >= exercises.length - 1) {
      return {
        'error': 'Cannot skip - this is the last exercise!',
        'message': 'You\'re on the final exercise. Let\'s finish strong!',
        'current_exercise': exercises[_currentExerciseIndex]['name'],
        'exercise_number': _currentExerciseIndex + 1,
        'total_exercises': exercises.length,
      };
    }
    
    final skippedExercise = exercises[_currentExerciseIndex];
    
    // Move to next exercise
    _currentExerciseIndex++;
    _currentSetIndex = 0;
    
    final nextExercise = exercises[_currentExerciseIndex];
    _updateWorkoutState();
    
    return {
      'skipped': skippedExercise['name'],
      'next_exercise': nextExercise['name'],
      'sets': nextExercise['sets'],
      'target_reps': nextExercise['reps'][0],
      'target_weight': nextExercise['weight'][0],
      'message': 'Skipped ${skippedExercise['name']}. Moving to ${nextExercise['name']} (exercise ${_currentExerciseIndex + 1} of ${exercises.length}).',
    };
  }
  
  /// Add more time to current rest period
  Map<String, dynamic> _addRestTime(int? additionalSeconds) {
    if (!_isResting) {
      return {'error': 'Not currently resting'};
    }
    
    final secondsToAdd = additionalSeconds ?? 30;
    _restTimeRemaining += secondsToAdd;
    
    return {
      'added_seconds': secondsToAdd,
      'total_remaining': _restTimeRemaining,
      'message': 'Added $secondsToAdd seconds. Total rest time: $_restTimeRemaining seconds.',
    };
  }
  
  /// Replace current exercise with a different one
  Future<Map<String, dynamic>> _replaceExercise(String? newExerciseName, String? muscleGroup) async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    try {
      final exercises = _currentWorkout!['exercises'] as List;
      if (_currentExerciseIndex >= exercises.length) {
        return {'error': 'Invalid exercise index'};
      }
      
      final currentExercise = exercises[_currentExerciseIndex];
      
      // If no exercise name provided, just return the current state
      if (newExerciseName == null || newExerciseName.isEmpty) {
        // If only muscle group is provided, search for similar exercises
        if (muscleGroup != null && muscleGroup.isNotEmpty) {
          final query = _supabase.from('exercises')
              .select('*')
              .ilike('primary_muscle', '%$muscleGroup%')
              .limit(5);
          
          final results = await query;
          
          if (results.isEmpty) {
            return {
              'error': 'No exercises found for muscle group: $muscleGroup',
              'message': 'Try a different muscle group like "chest", "back", "legs", etc.',
            };
          }
          
          // Return options for the AI to choose from
          return {
            'current_exercise': currentExercise['name'],
            'muscle_group': muscleGroup,
            'available_exercises': results.map((e) => {
              'name': e['name'],
              'muscle': e['primary_muscle'],
              'equipment': e['equipment'] ?? 'None',
            }).toList(),
            'message': 'Found ${results.length} exercises for $muscleGroup. Please specify which exercise you want.',
          };
        }
        
        return {'error': 'Please specify an exercise name to replace with'};
      }
      
      // Search for the specific exercise
      final searchQuery = _supabase.from('exercises')
          .select('*')
          .ilike('name', '%$newExerciseName%')
          .limit(1);
      
      final results = await searchQuery;
      
      if (results.isEmpty) {
        return {
          'error': 'Exercise not found: $newExerciseName',
          'message': 'Please try a different exercise name',
        };
      }
      
      final newExercise = results[0];
      
      // Important: Only update the exercise name and related info, keep the structure
      exercises[_currentExerciseIndex] = {
        ...currentExercise,
        'exercise_id': newExercise['id'],
        'name': newExercise['name'],
        'primary_muscle': newExercise['primary_muscle'] ?? currentExercise['primary_muscle'],
        'equipment': newExercise['equipment'] ?? currentExercise['equipment'],
        // Keep the original sets, reps, weight, etc.
        'sets': currentExercise['sets'],
        'reps': currentExercise['reps'],
        'weight': currentExercise['weight'],
        'rest': currentExercise['rest'],
        'order': currentExercise['order'],
      };
      
      // Only reset completed sets for this exercise, not the set index
      // This prevents issues with workout progression
      if (_currentWorkout!.containsKey('completed_sets')) {
        final completedSets = _currentWorkout!['completed_sets'] as List;
        // Remove any completed sets for this exercise
        completedSets.removeWhere((set) => set['exercise_index'] == _currentExerciseIndex);
      }
      
      _updateWorkoutState();
      
      // Update AI context with new workout info
      _updateAIContext();
      
      debugPrint('✅ Exercise replaced successfully');
      debugPrint('   Old: ${currentExercise['name']}');
      debugPrint('   New: ${newExercise['name']}');
      debugPrint('   Muscle: ${newExercise['primary_muscle']}');
      debugPrint('   Equipment: ${newExercise['equipment']}');
      
      return {
        'replaced': currentExercise['name'],
        'new_exercise': newExercise['name'],
        'primary_muscle': newExercise['primary_muscle'],
        'equipment': newExercise['equipment'] ?? 'None',
        'sets': currentExercise['sets'],
        'message': 'Successfully replaced ${currentExercise['name']} with ${newExercise['name']}. You still have ${currentExercise['sets']} sets to complete.',
      };
    } catch (e) {
      debugPrint('❌ Error replacing exercise: $e');
      return {'error': 'Failed to replace exercise: ${e.toString()}'};
    }
  }
  
  /// Search for available exercises
  Future<Map<String, dynamic>> _searchExercises(String? muscleGroup, String? equipment) async {
    try {
      var query = _supabase.from('exercises').select('id, name, primary_muscle, equipment');
      
      if (muscleGroup != null && muscleGroup.isNotEmpty) {
        query = query.ilike('primary_muscle', '%$muscleGroup%');
      }
      
      if (equipment != null && equipment.isNotEmpty) {
        query = query.ilike('equipment', '%$equipment%');
      }
      
      final results = await query.limit(10);
      
      if (results.isEmpty) {
        return {
          'exercises': [],
          'message': 'No exercises found matching your criteria',
        };
      }
      
      // Add context about current workout
      String contextMessage = 'Found ${results.length} exercises';
      if (_currentWorkout != null) {
        final exercises = _currentWorkout!['exercises'] as List;
        contextMessage += '. Note: You can use these to replace exercises in your current workout (${exercises.length} exercises total).';
      }
      
      return {
        'exercises': results.map((e) => {
          'name': e['name'],
          'muscle': e['primary_muscle'],
          'equipment': e['equipment'] ?? 'None',
        }).toList(),
        'total': results.length,
        'message': contextMessage,
        'note': 'These exercises can be used with the replace_exercise function to swap out exercises in your current workout.',
      };
    } catch (e) {
      debugPrint('Error searching exercises: $e');
      return {'error': 'Failed to search exercises: ${e.toString()}'};
    }
  }
  
  /// Handle errors
  void _handleError(Map<String, dynamic> event) {
    final error = event['error'];
    final message = error['message'] ?? 'Unknown error';
    debugPrint('Error from server: $message');
    _errorController.add(message);
  }
  
  /// Send an event through the data channel
  void _sendEvent(Map<String, dynamic> event) {
    if (_dataChannel?.state == RTCDataChannelState.RTCDataChannelOpen) {
      final message = jsonEncode(event);
      _dataChannel!.send(RTCDataChannelMessage(message));
      // Only log important events
      if (!['response.create'].contains(event['type'])) {
        debugPrint('📤 Sent: ${event['type']}');
      }
    } else {
      debugPrint('❌ Cannot send event - data channel not open');
    }
  }
  
  /// Send a text message
  void sendTextMessage(String text) {
    final event = {
      'type': 'conversation.item.create',
      'item': {
        'type': 'message',
        'role': 'user',
        'content': [
          {
            'type': 'input_text',
            'text': text,
          }
        ],
      },
    };
    
    _sendEvent(event);
    
    // Trigger response only if not already processing
    if (!_isProcessingResponse) {
      _isProcessingResponse = true;
      _sendEvent({'type': 'response.create'});
    }
  }
  
  /// Disconnect and clean up
  Future<void> disconnect() async {
    debugPrint('Disconnecting WebRTC...');
    
    // Close data channel
    await _dataChannel?.close();
    _dataChannel = null;
    
    // Stop local stream
    _localStream?.getTracks().forEach((track) {
      track.stop();
    });
    await _localStream?.dispose();
    _localStream = null;
    
    // Close peer connection
    await _peerConnection?.close();
    _peerConnection = null;
    
    debugPrint('WebRTC disconnected');
  }
  
  /// Dispose of resources
  void dispose() {
    _restTimer?.cancel();
    disconnect();
    _connectionStateController.close();
    _transcriptController.close();
    _errorController.close();
    _workoutStateController.close();
  }
}

/// Event for transcript updates
class TranscriptEvent {
  final String text;
  final bool isUser;
  final bool isDelta;
  final bool isComplete;
  
  TranscriptEvent({
    required this.text,
    required this.isUser,
    this.isDelta = false,
    this.isComplete = false,
  });
}