import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import '../../core/constants/supabase_constants.dart';

/// Enhanced Supabase service with query optimization and caching
class SupabaseService {
  static final Map<String, dynamic> _queryCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConstants.supabaseUrl,
      anonKey: SupabaseConstants.supabaseAnonKey,
    );
  }

  static SupabaseClient get client => Supabase.instance.client;

  static User? get currentUser => client.auth.currentUser;

  static Stream<AuthState> get authStateChanges =>
      client.auth.onAuthStateChange;

  /// Cached query execution with automatic cache invalidation
  static Future<T> cachedQuery<T>(
    String cacheKey,
    Future<T> Function() queryFunction, {
    Duration? customExpiry,
    bool forceRefresh = false,
  }) async {
    final expiry = customExpiry ?? _cacheExpiry;

    // Check cache validity
    if (!forceRefresh && _queryCache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null && DateTime.now().difference(timestamp) < expiry) {
        debugPrint('📦 Cache hit for: $cacheKey');
        return _queryCache[cacheKey] as T;
      }
    }

    // Execute query and cache result
    debugPrint('🔄 Cache miss, executing query: $cacheKey');
    final result = await queryFunction();
    _queryCache[cacheKey] = result;
    _cacheTimestamps[cacheKey] = DateTime.now();

    return result;
  }

  /// Clear specific cache entry
  static void clearCache(String cacheKey) {
    _queryCache.remove(cacheKey);
    _cacheTimestamps.remove(cacheKey);
    debugPrint('🗑️ Cleared cache for: $cacheKey');
  }

  /// Clear all cache
  static void clearAllCache() {
    _queryCache.clear();
    _cacheTimestamps.clear();
    debugPrint('🗑️ Cleared all cache');
  }

  /// Optimized workout query with minimal data fetching
  static Future<List<Map<String, dynamic>>> getOptimizedWorkouts({
    required String userId,
    bool includeExercises = false,
    int limit = 20,
    int offset = 0,
  }) async {
    final cacheKey = 'workouts_${userId}_${includeExercises}_${limit}_$offset';

    return cachedQuery(cacheKey, () async {
      String selectQuery = '''
        id,
        name,
        ai_description,
        is_completed,
        start_time,
        end_time,
        created_at
      ''';

      if (includeExercises) {
        selectQuery += ''',
          workout_exercises!inner (
            id,
            name,
            sets,
            reps,
            weight,
            rest_interval,
            completed,
            exercises!inner (
              id,
              name,
              primary_muscle,
              equipment
            )
          )
        ''';
      }

      var query = client
          .from('workouts')
          .select(selectQuery)
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return await query;
    });
  }

  /// Optimized user stats query with aggregation
  static Future<Map<String, dynamic>> getOptimizedUserStats(String userId) async {
    final cacheKey = 'user_stats_$userId';

    return cachedQuery(cacheKey, () async {
      // Use RPC for server-side aggregation
      return await client.rpc('get_user_stats_optimized',
        params: {'user_id_param': userId});
    }, customExpiry: const Duration(minutes: 10));
  }

  static Future<void> signUp({
    required String email,
    required String password,
  }) async {
    await client.auth.signUp(
      email: email,
      password: password,
    );
  }

  static Future<void> signIn({
    required String email,
    required String password,
  }) async {
    await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  static Future<void> signOut() async {
    clearAllCache(); // Clear cache on logout
    await client.auth.signOut();
  }
}
