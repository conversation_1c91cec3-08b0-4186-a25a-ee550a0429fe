import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class WorkoutChatService {
  // ChatTrigger node in "Hosted Chat" mode - requires /chat suffix
  static const _prodChatUrl =
      'https://sciwell.app.n8n.cloud/webhook/f0137805-14b7-4af7-8143-73d2935d28ba/chat';
  static const _testChatUrl =
      'https://sciwell.app.n8n.cloud/webhook-test/f0137805-14b7-4af7-8143-73d2935d28ba/chat';
  
  String? _sessionId;
  final String userId;
  
  WorkoutChatService({required this.userId});
  
  /// Initialize or load existing chat session
  Future<void> initializeSession() async {
    await _loadOrCreateSession();
  }
  
  /// Load existing session or create new one
  Future<void> _loadOrCreateSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _sessionId = prefs.getString('chat_session_id_$userId');
      
      if (_sessionId == null) {
        _sessionId = '${userId}_${DateTime.now().millisecondsSinceEpoch}';
        await prefs.setString('chat_session_id_$userId', _sessionId!);
        debugPrint('💬 Created new chat session: $_sessionId');
      } else {
        debugPrint('💬 Loaded existing chat session: $_sessionId');
      }
    } catch (e) {
      // Fallback if SharedPreferences fails
      _sessionId = '${userId}_${DateTime.now().millisecondsSinceEpoch}';
      debugPrint('⚠️ SharedPreferences failed, using fallback session: $_sessionId');
    }
  }
  
  /// Send message to n8n chat workflow
  Future<String> sendMessage(
    String message, {
    bool useTestEndpoint = false, // Use production endpoint since chat is publicly available
  }) async {
    if (_sessionId == null) {
      await initializeSession();
    }
    
    final url = useTestEndpoint ? _testChatUrl : _prodChatUrl;
    final payload = {
      'sessionId': _sessionId,
      'action': 'sendMessage',
      'chatInput': message,
      'user_id': userId,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    debugPrint('💬 SENDING CHAT MESSAGE TO N8N:');
    debugPrint('  URL: $url');
    debugPrint('  Endpoint: ${useTestEndpoint ? 'TEST' : 'PRODUCTION'}');
    debugPrint('  Session ID: $_sessionId');
    debugPrint('  User ID: $userId');
    debugPrint('  Message: $message');
    debugPrint('');
    debugPrint('📋 CHAT PAYLOAD:');
    debugPrint(jsonEncode(payload));
    debugPrint('');
    
    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(payload),
      );
      
      debugPrint('💬 CHAT RESPONSE:');
      debugPrint('  Status Code: ${response.statusCode}');
      debugPrint('  Response Body: ${response.body}');
      debugPrint('');
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Try to parse JSON response first
        try {
          final data = jsonDecode(response.body);
          
          // Handle different response formats from n8n
          if (data is Map<String, dynamic>) {
            // Look for common response fields
            final botResponse = data['output'] ?? 
                               data['response'] ?? 
                               data['message'] ?? 
                               data['text'] ??
                               data.toString();
            
            debugPrint('✅ Chat message sent successfully');
            return botResponse.toString();
          } else if (data is String) {
            debugPrint('✅ Chat message sent successfully');
            return data;
          } else {
            debugPrint('✅ Chat message sent successfully (unknown format)');
            return data.toString();
          }
        } catch (e) {
          // If JSON parsing fails, return plain text response
          debugPrint('✅ Chat message sent successfully (plain text)');
          return response.body;
        }
      } else {
        debugPrint('❌ Chat error ${response.statusCode}: ${response.body}');
        throw Exception('Chat service responded with status ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Failed to send chat message: $e');
      rethrow;
    }
  }
  
  /// Clear current session (for logout or reset)
  Future<void> clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('chat_session_id_$userId');
      _sessionId = null;
      debugPrint('💬 Chat session cleared for user: $userId');
    } catch (e) {
      debugPrint('⚠️ Failed to clear chat session: $e');
    }
  }

  /// Start a new session (creates new session ID)
  Future<void> startNewSession() async {
    try {
      // Clear existing session
      await clearSession();
      
      // Create new session
      _sessionId = '${userId}_${DateTime.now().millisecondsSinceEpoch}';
      
      // Save new session
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('chat_session_id_$userId', _sessionId!);
      
      debugPrint('💬 Started new chat session: $_sessionId');
    } catch (e) {
      debugPrint('⚠️ Failed to start new session: $e');
      // Fallback to in-memory session
      _sessionId = '${userId}_${DateTime.now().millisecondsSinceEpoch}';
    }
  }
  
  /// Get current session ID
  String? get sessionId => _sessionId;
  
  /// Send test message to verify n8n integration
  static Future<void> sendTestMessage({String testUserId = 'test-user-123'}) async {
    final chatService = WorkoutChatService(userId: testUserId);
    
    try {
      debugPrint('🧪 TESTING CHAT SERVICE');
      final response = await chatService.sendMessage(
        'Hello! This is a test message from Flutter app to verify n8n chat integration.',
        useTestEndpoint: true,
      );
      debugPrint('🧪 TEST RESPONSE: $response');
    } catch (e) {
      debugPrint('🧪 TEST FAILED: $e');
    }
  }
}